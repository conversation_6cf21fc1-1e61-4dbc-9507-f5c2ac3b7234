2025-07-27 12:20:26,882 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:20:26,882 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:20:26,930 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:20:26,931 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:20:27,644 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:20:31,674 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:20:31,675 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:20:31,692 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:20:31,886 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:20:31,886 - INFO - ============================================================
2025-07-27 12:20:31,886 - INFO - 📊 Tổng records: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:20:31,886 - INFO - 📊 Còn lại: 25989
2025-07-27 12:20:31,886 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:20:31,886 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:20:31,888 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:20:31,889 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,892 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,895 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,896 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,898 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,899 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,900 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.66 10.764, 106.66 10.763, 106.66 10.762, 106.661 10.761...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Minh Phụng', 'code': '27238'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,901 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,902 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,903 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,904 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,905 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,909 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.205 13.781, 109.205 13.781, 109.205 13.781, 109.205 13....>, 'geo_province_code': '52', 'province_title': 'Tỉnh Gia Lai', 'ward_title': 'Phường Quy Nhơn Nam', 'code': '21592'}
2025-07-27 12:20:31,910 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.103 10.943, 108.104 10.942, 108.104 10.942, 108.104 10....>, 'geo_province_code': '68', 'province_title': 'Tỉnh Lâm Đồng', 'ward_title': 'Phường Phan Thiết', 'code': '22945'}
2025-07-27 12:20:31,913 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:31,915 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,916 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:31,921 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,924 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,925 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,926 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,927 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,928 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.827 10.76, 106.827 10.76, 106.827 10.76, 106.829 10.761...>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Xã Đại Phước', 'code': '26491'}
2025-07-27 12:20:31,929 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,931 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:31,932 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,933 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,944 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((107.542 16.482, 107.542 16.482, 107.542 16.482, 107.542 16....>, 'geo_province_code': '46', 'province_title': 'Thành phố Huế', 'ward_title': 'Phường Kim Long', 'code': '19774'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,945 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.837 21.011, 105.837 21.011, 105.837 21.011, 105.838 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kim Liên', 'code': '00229'}
2025-07-27 12:20:31,946 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,947 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:31,949 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,951 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:31,952 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.69 10.751, 106.69 10.75, 106.69 10.75, 106.69 10.75, 10...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chánh Hưng', 'code': '27418'}
2025-07-27 12:20:31,953 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.675 10.761, 106.675 10.76, 106.676 10.759, 106.676 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường An Đông', 'code': '27316'}
2025-07-27 12:20:31,954 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.636 10.769, 106.636 10.769, 106.638 10.769, 106.64 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Thới', 'code': '27232'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.777, 106.685 10.776, 106.685 10.776, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bàn Cờ', 'code': '27154'}
2025-07-27 12:20:31,955 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,956 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.613 10.781, 106.613 10.78, 106.613 10.78, 106.613 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Trị Đông', 'code': '27448'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.705 10.792, 106.706 10.792, 106.706 10.792, 106.706 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Sài Gòn', 'code': '26740'}
2025-07-27 12:20:31,957 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,958 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,959 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.809, 106.649 10.809, 106.65 10.809, 106.651 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bảy Hiền', 'code': '26983'}
2025-07-27 12:20:31,960 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.718 10.814, 106.718 10.814, 106.718 10.814, 106.718 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thạnh Mỹ Tây', 'code': '26956'}
2025-07-27 12:20:31,961 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,962 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:31,963 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,964 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,965 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,966 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:31,968 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:31,969 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:31,970 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:31,971 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,972 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.765, 106.682 10.765, 106.682 10.765, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Chợ Quán', 'code': '27301'}
2025-07-27 12:20:31,973 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,974 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.638 10.796, 106.638 10.796, 106.639 10.795, 106.639 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Phú', 'code': '27031'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,975 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:31,976 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:31,977 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 20.988, 105.827 20.987, 105.827 20.987, 105.827 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Định Công', 'code': '00316'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:31,978 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:31,979 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,980 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.77 20.965, 105.77 20.965, 105.77 20.965, 105.77 20.964,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Kiến Hưng', 'code': '09552'}
2025-07-27 12:20:31,981 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.851 21.041, 105.851 21.04, 105.851 21.04, 105.851 21.04...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hoàn Kiếm', 'code': '00070'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.685 10.768, 106.685 10.768, 106.686 10.768, 106.686 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Cầu Ông Lãnh', 'code': '26758'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.686 10.792, 106.69 10.789, 106.69 10.789, 106.691 10.78...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Xuân Hòa', 'code': '27139'}
2025-07-27 12:20:31,982 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.682 10.791, 106.682 10.791, 106.682 10.791, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Nhiêu Lộc', 'code': '27142'}
2025-07-27 12:20:31,983 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.701 10.765, 106.701 10.765, 106.702 10.764, 106.704 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Khánh Hội', 'code': '27265'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:31,984 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.691 10.811, 106.691 10.811, 106.691 10.811, 106.692 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Gia Định', 'code': '26944'}
2025-07-27 12:20:31,985 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.668 10.8, 106.668 10.8, 106.668 10.8, 106.668 10.8, 106...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Nhuận', 'code': '27073'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.684 10.834, 106.684 10.834, 106.684 10.834, 106.685 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hạnh Thông', 'code': '26890'}
2025-07-27 12:20:31,986 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.653 10.79, 106.653 10.79, 106.653 10.79, 106.653 10.789...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Hòa', 'code': '26995'}
2025-07-27 12:20:31,988 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:31,989 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:31,992 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:31,997 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:31,998 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.62 10.785, 106.621 10.785, 106.621 10.785, 106.621 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Thạnh', 'code': '27028'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.664 10.777, 106.667 10.768, 106.667 10.768, 106.669 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Diên Hồng', 'code': '27169'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:31,999 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.698 10.76, 106.701 10.756, 106.701 10.756, 106.702 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vĩnh Hội', 'code': '27286'}
2025-07-27 12:20:32,000 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,001 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.694 10.725, 106.695 10.725, 106.695 10.725, 106.696 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Xã Nhà Bè', 'code': '27655'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,002 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,003 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.891 21.051, 105.891 21.051, 105.892 21.051, 105.892 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Long Biên', 'code': '00145'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,004 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.121 9.208, 105.122 9.207, 105.123 9.206, 105.123 9.206,...>, 'geo_province_code': '96', 'province_title': 'Tỉnh Cà Mau', 'ward_title': 'Phường Lý Văn Lâm', 'code': '32014'}
2025-07-27 12:20:32,005 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,006 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.753 10.046, 105.753 10.046, 105.754 10.046, 105.755 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Tân An', 'code': '31147'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,009 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.681 10.778, 106.682 10.778, 106.682 10.778, 106.682 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Vườn Lài', 'code': '27190'}
2025-07-27 12:20:32,010 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,011 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.66 18.697, 105.661 18.697, 105.661 18.696, 105.661 18.6...>, 'geo_province_code': '40', 'province_title': 'Tỉnh Nghệ An', 'ward_title': 'Phường Thành Vinh', 'code': '16681'}
2025-07-27 12:20:32,012 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,015 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.802 10.977, 106.802 10.977, 106.802 10.977, 106.802 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Trấn Biên', 'code': '26041'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,018 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,019 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,020 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.666 10.75, 106.667 10.75, 106.667 10.75, 106.669 10.75,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Phú Định', 'code': '27427'}
2025-07-27 12:20:32,021 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,022 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.67 10.784, 106.67 10.784, 106.67 10.784, 106.67 10.784,...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Hòa Hưng', 'code': '27163'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.663 10.809, 106.663 10.809, 106.664 10.809, 106.664 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn Nhất', 'code': '26968'}
2025-07-27 12:20:32,023 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,031 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.86 18.344, 105.86 18.344, 105.861 18.344, 105.861 18.34...>, 'geo_province_code': '42', 'province_title': 'Tỉnh Hà Tĩnh', 'ward_title': 'Phường Hà Huy Tập', 'code': '18652'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.467 9.784, 105.467 9.784, 105.467 9.784, 105.468 9.784,...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Vị Thanh', 'code': '31321'}
2025-07-27 12:20:32,032 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.995 10.264, 105.995 10.264, 105.996 10.264, 105.996 10....>, 'geo_province_code': '86', 'province_title': 'Tỉnh Vĩnh Long', 'ward_title': 'Phường Thanh Đức', 'code': '29590'}
2025-07-27 12:20:32,038 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((104.843 21.713, 104.845 21.712, 104.849 21.712, 104.852 21....>, 'geo_province_code': '15', 'province_title': 'Tỉnh Lào Cai', 'ward_title': 'Phường Âu Lâu', 'code': '04543'}
2025-07-27 12:20:32,046 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.097 12.696, 108.097 12.696, 108.097 12.696, 108.097 12....>, 'geo_province_code': '66', 'province_title': 'Tỉnh Đắk Lắk', 'ward_title': 'Phường Tân Lập', 'code': '24121'}
2025-07-27 12:20:32,047 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.649 10.997, 106.65 10.997, 106.65 10.997, 106.651 10.99...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Thủ Dầu Một', 'code': '25747'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.742 10.927, 106.741 10.913, 106.741 10.909, 106.741 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hòa', 'code': '25987'}
2025-07-27 12:20:32,049 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,050 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.822 21.093, 105.826 21.092, 105.831 21.09, 105.831 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Hồng Hà', 'code': '00097'}
2025-07-27 12:20:32,051 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,052 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,054 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.772 10.965, 106.771 10.963, 106.771 10.963, 106.772 10....>, 'geo_province_code': '75', 'province_title': 'Tỉnh Đồng Nai', 'ward_title': 'Phường Biên Hòa', 'code': '26068'}
2025-07-27 12:20:32,057 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,058 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.821 21.003, 105.823 21.002, 105.823 21.002, 105.823 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Khương Đình', 'code': '00364'}
2025-07-27 12:20:32,061 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.775 10.017, 105.776 10.016, 105.776 10.016, 105.777 10....>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Cái Răng', 'code': '31186'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,062 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,063 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.826 21.021, 105.828 21.019, 105.829 21.019, 105.829 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Đống Đa', 'code': '00235'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,066 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.806 10.041, 105.809 10.036, 105.814 10.03, 105.814 10.0...>, 'geo_province_code': '92', 'province_title': 'Thành phố Cần Thơ', 'ward_title': 'Phường Hưng Phú', 'code': '31201'}
2025-07-27 12:20:32,070 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,071 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.841 20.998, 105.841 20.998, 105.842 20.997, 105.843 20....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Tương Mai', 'code': '00322'}
2025-07-27 12:20:32,072 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.661 10.742, 106.661 10.742, 106.661 10.742, 106.661 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Đông', 'code': '27424'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.706 10.837, 106.707 10.836, 106.708 10.835, 106.708 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Lợi Trung', 'code': '26905'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.603 10.826, 106.604 10.826, 106.604 10.826, 106.606 10....>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bình Hưng Hòa', 'code': '27439'}
2025-07-27 12:20:32,073 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.644 10.811, 106.644 10.811, 106.645 10.81, 106.645 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Bình', 'code': '27004'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,074 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,075 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.715 10.738, 106.716 10.738, 106.717 10.738, 106.72 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Mỹ', 'code': '27487'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.859 21.009, 105.859 21.009, 105.86 21.009, 105.861 21.0...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Bạch Mai', 'code': '00292'}
2025-07-27 12:20:32,076 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.695 10.78, 106.696 10.779, 106.696 10.778, 106.696 10.7...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Bến Thành', 'code': '26743'}
2025-07-27 12:20:32,077 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((108.203 16.064, 108.203 16.064, 108.204 16.063, 108.204 16....>, 'geo_province_code': '48', 'province_title': 'Thành phố Đà Nẵng', 'ward_title': 'Phường Hòa Cường', 'code': '20257'}
2025-07-27 12:20:32,082 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((109.159 12.251, 109.16 12.251, 109.16 12.251, 109.161 12.25...>, 'geo_province_code': '56', 'province_title': 'Tỉnh Khánh Hòa', 'ward_title': 'Phường Nam Nha Trang', 'code': '22402'}
2025-07-27 12:20:32,083 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((106.65 10.837, 106.654 10.836, 106.656 10.836, 106.657 10.8...>, 'geo_province_code': '79', 'province_title': 'Thành phố Hồ Chí Minh', 'ward_title': 'Phường Tân Sơn', 'code': '27007'}
2025-07-27 12:20:32,084 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.842 21.029, 105.843 21.028, 105.843 21.028, 105.843 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cửa Nam', 'code': '00082'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.807 21.037, 105.807 21.037, 105.808 21.036, 105.808 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Giảng Võ', 'code': '00025'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.811 21.01, 105.811 21.01, 105.811 21.01, 105.813 21.009...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Thanh Xuân', 'code': '00367'}
2025-07-27 12:20:32,085 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.798 21.025, 105.798 21.024, 105.798 21.024, 105.798 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Yên Hòa', 'code': '00175'}
2025-07-27 12:20:32,086 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Tìm thấy ward cho record {'geometry': <MULTIPOLYGON (((105.801 21.03, 105.801 21.03, 105.801 21.03, 105.802 21.03,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Láng', 'code': '00199'}
2025-07-27 12:20:32,087 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:20:33,718 - INFO - 🚀 Xử lý 400 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:36,209 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:21:36,209 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:21:36,245 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:21:36,245 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:21:36,594 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:21:41,093 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:21:41,095 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:21:41,111 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:21:41,273 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:21:41,273 - INFO - ============================================================
2025-07-27 12:21:41,273 - INFO - 📊 Tổng records: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:21:41,273 - INFO - 📊 Còn lại: 25989
2025-07-27 12:21:41,273 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:21:41,273 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:21:41,275 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:21:41,347 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:42,806 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:45,640 - INFO - 📊 Progress: 200 requests | 1272.4 RPM | 100.0% success
2025-07-27 12:21:48,715 - INFO - 📊 Batch RPM: 2721.3 RPM (268 records trong 5.91s)
2025-07-27 12:21:48,715 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:21:48,715 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:21:48,720 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:48,720 - INFO - 😴 Xử lý batch trong 7.45s, nghỉ 53.553420066833496 giây để tránh rate limit...
2025-07-27 12:21:48,720 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:21:48,759 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:21:48,819 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:49,400 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:51,520 - INFO - 📊 Progress: 400 requests | 1567.5 RPM | 100.0% success
2025-07-27 12:21:55,101 - INFO - 📊 Batch RPM: 2852.3 RPM (271 records trong 5.70s)
2025-07-27 12:21:55,101 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 12:21:55,101 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 12:21:55,105 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:21:55,105 - INFO - 😴 Xử lý batch trong 6.38s, nghỉ 54.6150860786438 giây để tránh rate limit...
2025-07-27 12:21:55,105 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 800)
2025-07-27 12:21:55,131 - INFO - 📊 Batch 3: 400 records
2025-07-27 12:21:55,192 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:21:55,586 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:21:56,156 - INFO - 📊 Progress: 600 requests | 1804.8 RPM | 100.0% success
2025-07-27 12:22:00,847 - INFO - 📊 Progress: 800 requests | 1948.2 RPM | 100.0% success
2025-07-27 12:22:00,884 - INFO - 📊 Batch RPM: 3023.8 RPM (267 records trong 5.30s)
2025-07-27 12:22:00,884 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 12:22:00,884 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 12:22:00,888 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:00,888 - INFO - 😴 Xử lý batch trong 5.78s, nghỉ 55.21640396118164 giây để tránh rate limit...
2025-07-27 12:22:00,888 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 1200)
2025-07-27 12:22:00,917 - INFO - 📊 Batch 4: 400 records
2025-07-27 12:22:00,997 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:01,378 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:02,603 - INFO - 📊 Progress: 1000 requests | 2273.3 RPM | 100.0% success
2025-07-27 12:22:03,400 - INFO - 📊 Batch RPM: 7566.3 RPM (255 records trong 2.02s)
2025-07-27 12:22:03,401 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 12:22:03,401 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 12:22:03,404 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:03,404 - INFO - 😴 Xử lý batch trong 2.52s, nghỉ 58.48449206352234 giây để tránh rate limit...
2025-07-27 12:22:03,404 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 12:22:03,432 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 1600)
2025-07-27 12:22:03,433 - INFO - 📊 Batch 5: 400 records
2025-07-27 12:22:03,706 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:04,118 - INFO - 🚀 Xử lý 229 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14285: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14277: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14329: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,154 - ERROR - ❌ Error processing record 14322: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,157 - ERROR - ❌ Error processing record 14323: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,158 - ERROR - ❌ Error processing record 14280: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14279: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,161 - ERROR - ❌ Error processing record 14272: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14340: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14282: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,162 - ERROR - ❌ Error processing record 14348: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14309: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14337: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14301: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14350: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14327: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14317: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14304: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14319: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14303: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,163 - ERROR - ❌ Error processing record 14336: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14305: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,164 - ERROR - ❌ Error processing record 14332: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,241 - ERROR - ❌ Error processing record 14366: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,246 - ERROR - ❌ Error processing record 14363: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,261 - ERROR - ❌ Error processing record 14368: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,293 - ERROR - ❌ Error processing record 14370: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,306 - INFO - 📊 Progress: 1200 requests | 2474.5 RPM | 100.0% success
2025-07-27 12:22:05,324 - ERROR - ❌ Error processing record 14385: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14375: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,326 - ERROR - ❌ Error processing record 14371: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,327 - ERROR - ❌ Error processing record 14380: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,330 - ERROR - ❌ Error processing record 14379: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,356 - ERROR - ❌ Error processing record 14388: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,386 - ERROR - ❌ Error processing record 14389: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,397 - ERROR - ❌ Error processing record 14391: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,409 - ERROR - ❌ Error processing record 14392: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,415 - ERROR - ❌ Error processing record 14393: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,416 - ERROR - ❌ Error processing record 14395: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,418 - ERROR - ❌ Error processing record 14400: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,421 - ERROR - ❌ Error processing record 14402: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,425 - ERROR - ❌ Error processing record 14406: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,428 - ERROR - ❌ Error processing record 14405: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,448 - ERROR - ❌ Error processing record 14407: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,449 - ERROR - ❌ Error processing record 14409: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14408: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,452 - ERROR - ❌ Error processing record 14410: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,453 - ERROR - ❌ Error processing record 14411: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
]
2025-07-27 12:22:05,950 - INFO - 📊 Batch RPM: 7502.9 RPM (229 records trong 1.83s)
2025-07-27 12:22:05,950 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 12:22:05,951 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 12:22:05,956 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:22:05,956 - INFO - 😴 Xử lý batch trong 2.55s, nghỉ 58.44778108596802 giây để tránh rate limit...
2025-07-27 12:22:05,956 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 2000)
2025-07-27 12:22:05,985 - INFO - 📊 Batch 6: 400 records
2025-07-27 12:22:06,198 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:22:06,437 - ERROR - ❌ Lỗi trong quá trình xử lý: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count"
  quota_id: "GenerateContentPaidTierInputTokensPerModelPerMinute"
  quota_dimensions {
    key: "model"
    value: "gemini-2.5-flash-lite"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 4000000
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 53
}
]
2025-07-27 12:24:51,690 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:24:51,690 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:24:51,730 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:24:51,730 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:24:52,092 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:04,673 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:25:04,673 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:25:04,692 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:25:04,693 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:25:05,030 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:25:08,881 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:25:08,882 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:25:08,896 - INFO - 📄 File exports/brand_offices_updated.csv chưa tồn tại, bắt đầu từ đầu
2025-07-27 12:25:08,988 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:25:08,989 - INFO - ============================================================
2025-07-27 12:25:08,989 - INFO - 📊 Tổng records: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Đã xử lý: 0
2025-07-27 12:25:08,989 - INFO - 📊 Còn lại: 25989
2025-07-27 12:25:08,989 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:25:08,989 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 0)
2025-07-27 12:25:08,991 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:25:09,057 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:25:20,643 - INFO - 🚀 Xử lý 268 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:25:23,422 - INFO - 📊 Progress: 200 requests | 640.0 RPM | 100.0% success
2025-07-27 12:25:27,136 - INFO - 📊 Batch RPM: 2476.8 RPM (268 records trong 6.49s)
2025-07-27 12:25:27,136 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:25:27,136 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:25:27,148 - INFO - ✅ Đã tạo file mới và lưu 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:25:27,148 - INFO - 😴 Xử lý batch trong 18.16s, nghỉ 42.84077787399292 giây để tránh rate limit...
2025-07-27 12:26:09,990 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:26:10,020 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 400)
2025-07-27 12:26:10,021 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:26:10,088 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:26:10,620 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:26:44,978 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 12:26:44,979 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 12:26:45,006 - INFO - ✅ Kết nối database thành công!
2025-07-27 12:26:45,006 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 12:26:45,383 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 12:26:49,274 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 12:26:49,275 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 12:26:49,296 - INFO - 📊 Đã đọc 400 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 12:26:49,376 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 12:26:49,376 - INFO - ============================================================
2025-07-27 12:26:49,376 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 12:26:49,376 - INFO - ============================================================
2025-07-27 12:26:49,376 - INFO - 📊 Tổng records: 25989
2025-07-27 12:26:49,376 - INFO - 📊 Đã xử lý: 400
2025-07-27 12:26:49,376 - INFO - 📊 Còn lại: 25589
2025-07-27 12:26:49,376 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 12:26:49,376 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 12:26:49,380 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 400)
2025-07-27 12:26:49,380 - INFO - 📊 Batch 1: 400 records
2025-07-27 12:26:49,464 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:26:50,270 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:26:54,922 - INFO - 📊 Progress: 200 requests | 1206.9 RPM | 100.0% success
2025-07-27 12:26:59,528 - INFO - 📊 Batch RPM: 1678.6 RPM (259 records trong 9.26s)
2025-07-27 12:26:59,529 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 12:26:59,530 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 12:26:59,541 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:26:59,541 - INFO - 😴 Xử lý batch trong 10.17s, nghỉ 50.83455204963684 giây để tránh rate limit...
2025-07-27 12:27:50,378 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 12:27:50,435 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 800)
2025-07-27 12:27:50,435 - INFO - 📊 Batch 2: 400 records
2025-07-27 12:27:50,505 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:27:50,882 - INFO - 🚀 Xử lý 280 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:27:57,095 - INFO - 📊 Progress: 400 requests | 332.8 RPM | 100.0% success
2025-07-27 12:28:04,630 - INFO - 📊 Batch RPM: 1222.1 RPM (280 records trong 13.75s)
2025-07-27 12:28:04,631 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 12:28:04,631 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 12:28:04,634 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:28:04,634 - INFO - 😴 Xử lý batch trong 14.25s, nghỉ 46.74600386619568 giây để tránh rate limit...
2025-07-27 12:28:51,383 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 12:28:51,402 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 1200)
2025-07-27 12:28:51,402 - INFO - 📊 Batch 3: 400 records
2025-07-27 12:28:51,496 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:28:51,893 - INFO - 🚀 Xử lý 248 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:28:52,453 - INFO - 📊 Progress: 600 requests | 282.4 RPM | 100.0% success
2025-07-27 12:28:57,740 - INFO - 📊 Batch RPM: 2545.5 RPM (248 records trong 5.85s)
2025-07-27 12:28:57,740 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 12:28:57,742 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 12:28:57,754 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:28:57,755 - INFO - 😴 Xử lý batch trong 6.37s, nghỉ 54.63087606430054 giây để tránh rate limit...
2025-07-27 12:29:52,386 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 12:29:52,400 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 1600)
2025-07-27 12:29:52,400 - INFO - 📊 Batch 4: 400 records
2025-07-27 12:29:52,601 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:29:52,994 - INFO - 🚀 Xử lý 245 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:29:53,544 - INFO - 📊 Progress: 800 requests | 254.6 RPM | 100.0% success
2025-07-27 12:29:54,677 - INFO - 📊 Progress: 1000 requests | 316.3 RPM | 100.0% success
2025-07-27 12:29:55,194 - INFO - 📊 Batch RPM: 6682.9 RPM (245 records trong 2.20s)
2025-07-27 12:29:55,195 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 12:29:55,196 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 12:29:55,203 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:29:55,203 - INFO - 😴 Xử lý batch trong 2.82s, nghỉ 58.184059858322144 giây để tránh rate limit...
2025-07-27 12:30:53,390 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 12:30:53,413 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 2000)
2025-07-27 12:30:53,413 - INFO - 📊 Batch 5: 400 records
2025-07-27 12:30:53,490 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:30:53,876 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:30:56,027 - INFO - 📊 Progress: 1200 requests | 286.8 RPM | 100.0% success
2025-07-27 12:30:57,848 - INFO - 📊 Batch RPM: 3595.2 RPM (238 records trong 3.97s)
2025-07-27 12:30:57,849 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 12:30:57,850 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 12:30:57,860 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:30:57,860 - INFO - 😴 Xử lý batch trong 4.47s, nghỉ 56.53036069869995 giây để tránh rate limit...
2025-07-27 12:31:54,392 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 12:31:54,434 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 2400)
2025-07-27 12:31:54,434 - INFO - 📊 Batch 6: 400 records
2025-07-27 12:31:54,673 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:31:55,131 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:31:56,221 - INFO - 📊 Progress: 1400 requests | 269.9 RPM | 100.0% success
2025-07-27 12:31:59,367 - INFO - 📊 Batch RPM: 3372.1 RPM (238 records trong 4.23s)
2025-07-27 12:31:59,368 - INFO - 🔄 Đã xử lý 2400 records...
2025-07-27 12:31:59,369 - INFO - ✅ Hoàn thành batch 6: 400 records
2025-07-27 12:31:59,378 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:31:59,378 - INFO - 😴 Xử lý batch trong 4.98s, nghỉ 56.01561617851257 giây để tránh rate limit...
2025-07-27 12:32:55,395 - INFO - 🔄 Xử lý batch 7 (offset: 2400)
2025-07-27 12:32:55,433 - INFO - 📊 Lấy được 400 records brand_office (offset: 2400, excluded: 2800)
2025-07-27 12:32:55,434 - INFO - 📊 Batch 7: 400 records
2025-07-27 12:32:55,659 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:32:56,097 - INFO - 🚀 Xử lý 216 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:32:57,148 - INFO - 📊 Progress: 1600 requests | 257.9 RPM | 100.0% success
2025-07-27 12:33:00,058 - INFO - 📊 Batch RPM: 3273.1 RPM (216 records trong 3.96s)
2025-07-27 12:33:00,059 - INFO - 🔄 Đã xử lý 2800 records...
2025-07-27 12:33:00,060 - INFO - ✅ Hoàn thành batch 7: 400 records
2025-07-27 12:33:00,064 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:33:00,064 - INFO - 😴 Xử lý batch trong 4.67s, nghỉ 56.33333992958069 giây để tránh rate limit...
2025-07-27 12:33:56,414 - INFO - 🔄 Xử lý batch 8 (offset: 2800)
2025-07-27 12:33:56,463 - INFO - 📊 Lấy được 400 records brand_office (offset: 2800, excluded: 3200)
2025-07-27 12:33:56,464 - INFO - 📊 Batch 8: 400 records
2025-07-27 12:33:56,647 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:33:57,079 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:33:57,774 - INFO - 📊 Progress: 1800 requests | 249.5 RPM | 100.0% success
2025-07-27 12:34:04,026 - INFO - 📊 Batch RPM: 2202.6 RPM (255 records trong 6.95s)
2025-07-27 12:34:04,027 - INFO - 🔄 Đã xử lý 3200 records...
2025-07-27 12:34:04,027 - INFO - ✅ Hoàn thành batch 8: 400 records
2025-07-27 12:34:04,034 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:34:04,035 - INFO - 😴 Xử lý batch trong 7.62s, nghỉ 53.38120985031128 giây để tránh rate limit...
2025-07-27 12:34:57,418 - INFO - 🔄 Xử lý batch 9 (offset: 3200)
2025-07-27 12:34:57,487 - INFO - 📊 Lấy được 400 records brand_office (offset: 3200, excluded: 3600)
2025-07-27 12:34:57,488 - INFO - 📊 Batch 9: 400 records
2025-07-27 12:34:57,577 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:34:57,974 - INFO - 🚀 Xử lý 254 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:34:58,548 - INFO - 📊 Progress: 2000 requests | 243.1 RPM | 100.0% success
2025-07-27 12:35:04,608 - INFO - 📊 Progress: 2200 requests | 264.2 RPM | 100.0% success
2025-07-27 12:35:06,870 - INFO - 📊 Batch RPM: 1713.2 RPM (254 records trong 8.90s)
2025-07-27 12:35:06,871 - INFO - 🔄 Đã xử lý 3600 records...
2025-07-27 12:35:06,872 - INFO - ✅ Hoàn thành batch 9: 400 records
2025-07-27 12:35:06,881 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:35:06,881 - INFO - 😴 Xử lý batch trong 9.46s, nghỉ 51.537439823150635 giây để tránh rate limit...
2025-07-27 12:35:58,422 - INFO - 🔄 Xử lý batch 10 (offset: 3600)
2025-07-27 12:35:58,473 - INFO - 📊 Lấy được 400 records brand_office (offset: 3600, excluded: 4000)
2025-07-27 12:35:58,473 - INFO - 📊 Batch 10: 400 records
2025-07-27 12:35:58,566 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:35:58,958 - INFO - 🚀 Xử lý 254 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:36:00,635 - INFO - 📊 Progress: 2400 requests | 259.2 RPM | 100.0% success
2025-07-27 12:36:06,508 - INFO - 📊 Batch RPM: 2018.8 RPM (254 records trong 7.55s)
2025-07-27 12:36:06,508 - INFO - 🔄 Đã xử lý 4000 records...
2025-07-27 12:36:06,509 - INFO - ✅ Hoàn thành batch 10: 400 records
2025-07-27 12:36:06,518 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:36:06,518 - INFO - 😴 Xử lý batch trong 8.09s, nghỉ 52.905786752700806 giây để tránh rate limit...
2025-07-27 12:36:59,426 - INFO - 🔄 Xử lý batch 11 (offset: 4000)
2025-07-27 12:36:59,473 - INFO - 📊 Lấy được 400 records brand_office (offset: 4000, excluded: 4400)
2025-07-27 12:36:59,474 - INFO - 📊 Batch 11: 400 records
2025-07-27 12:36:59,575 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:36:59,989 - INFO - 🚀 Xử lý 262 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:37:01,003 - INFO - 📊 Progress: 2600 requests | 253.2 RPM | 100.0% success
2025-07-27 12:37:05,860 - INFO - 📊 Batch RPM: 2677.9 RPM (262 records trong 5.87s)
2025-07-27 12:37:05,861 - INFO - 🔄 Đã xử lý 4400 records...
2025-07-27 12:37:05,866 - INFO - ✅ Hoàn thành batch 11: 400 records
2025-07-27 12:37:05,876 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:37:05,877 - INFO - 😴 Xử lý batch trong 6.45s, nghỉ 54.54958367347717 giây để tránh rate limit...
2025-07-27 12:38:00,429 - INFO - 🔄 Xử lý batch 12 (offset: 4400)
2025-07-27 12:38:00,483 - INFO - 📊 Lấy được 400 records brand_office (offset: 4400, excluded: 4800)
2025-07-27 12:38:00,483 - INFO - 📊 Batch 12: 400 records
2025-07-27 12:38:00,645 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:38:01,159 - INFO - 🚀 Xử lý 252 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:38:01,731 - INFO - 📊 Progress: 2800 requests | 248.2 RPM | 100.0% success
2025-07-27 12:38:03,884 - INFO - 📊 Progress: 3000 requests | 265.1 RPM | 100.0% success
2025-07-27 12:38:03,991 - INFO - 📊 Batch RPM: 5341.5 RPM (252 records trong 2.83s)
2025-07-27 12:38:03,991 - INFO - 🔄 Đã xử lý 4800 records...
2025-07-27 12:38:03,993 - INFO - ✅ Hoàn thành batch 12: 400 records
2025-07-27 12:38:04,002 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:38:04,003 - INFO - 😴 Xử lý batch trong 3.57s, nghỉ 57.42817831039429 giây để tránh rate limit...
2025-07-27 12:39:01,434 - INFO - 🔄 Xử lý batch 13 (offset: 4800)
2025-07-27 12:39:01,469 - INFO - 📊 Lấy được 400 records brand_office (offset: 4800, excluded: 5200)
2025-07-27 12:39:01,469 - INFO - 📊 Batch 13: 400 records
2025-07-27 12:39:01,592 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:39:02,008 - INFO - 🚀 Xử lý 258 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:39:04,276 - INFO - 📊 Progress: 3200 requests | 259.7 RPM | 100.0% success
2025-07-27 12:39:07,924 - INFO - 📊 Batch RPM: 2616.6 RPM (258 records trong 5.92s)
2025-07-27 12:39:07,925 - INFO - 🔄 Đã xử lý 5200 records...
2025-07-27 12:39:07,925 - INFO - ✅ Hoàn thành batch 13: 400 records
2025-07-27 12:39:07,938 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:39:07,938 - INFO - 😴 Xử lý batch trong 6.50s, nghỉ 54.49626183509827 giây để tránh rate limit...
2025-07-27 12:40:02,439 - INFO - 🔄 Xử lý batch 14 (offset: 5200)
2025-07-27 12:40:02,494 - INFO - 📊 Lấy được 400 records brand_office (offset: 5200, excluded: 5600)
2025-07-27 12:40:02,494 - INFO - 📊 Batch 14: 400 records
2025-07-27 12:40:02,605 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:40:03,009 - INFO - 🚀 Xử lý 261 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:40:04,598 - INFO - 📊 Progress: 3400 requests | 255.1 RPM | 100.0% success
2025-07-27 12:40:09,059 - INFO - 📊 Batch RPM: 2588.7 RPM (261 records trong 6.05s)
2025-07-27 12:40:09,060 - INFO - 🔄 Đã xử lý 5600 records...
2025-07-27 12:40:09,061 - INFO - ✅ Hoàn thành batch 14: 400 records
2025-07-27 12:40:09,069 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:40:09,069 - INFO - 😴 Xử lý batch trong 6.63s, nghỉ 54.37018704414368 giây để tránh rate limit...
2025-07-27 12:41:03,443 - INFO - 🔄 Xử lý batch 15 (offset: 5600)
2025-07-27 12:41:03,508 - INFO - 📊 Lấy được 400 records brand_office (offset: 5600, excluded: 6000)
2025-07-27 12:41:03,509 - INFO - 📊 Batch 15: 400 records
2025-07-27 12:41:03,623 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:41:04,031 - INFO - 🚀 Xử lý 251 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:41:04,663 - INFO - 📊 Progress: 3600 requests | 251.3 RPM | 100.0% success
2025-07-27 12:41:09,582 - INFO - 📊 Batch RPM: 2713.0 RPM (251 records trong 5.55s)
2025-07-27 12:41:09,583 - INFO - 🔄 Đã xử lý 6000 records...
2025-07-27 12:41:09,583 - INFO - ✅ Hoàn thành batch 15: 400 records
2025-07-27 12:41:09,588 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:41:09,589 - INFO - 😴 Xử lý batch trong 6.14s, nghỉ 54.8560471534729 giây để tránh rate limit...
2025-07-27 12:42:04,448 - INFO - 🔄 Xử lý batch 16 (offset: 6000)
2025-07-27 12:42:04,505 - INFO - 📊 Lấy được 400 records brand_office (offset: 6000, excluded: 6400)
2025-07-27 12:42:04,506 - INFO - 📊 Batch 16: 400 records
2025-07-27 12:42:04,632 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:42:05,065 - INFO - 🚀 Xử lý 257 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:42:05,614 - INFO - 📊 Progress: 3800 requests | 247.7 RPM | 100.0% success
2025-07-27 12:42:11,237 - INFO - 📊 Progress: 4000 requests | 259.1 RPM | 100.0% success
2025-07-27 12:42:12,713 - INFO - 📊 Batch RPM: 2016.3 RPM (257 records trong 7.65s)
2025-07-27 12:42:12,714 - INFO - 🔄 Đã xử lý 6400 records...
2025-07-27 12:42:12,714 - INFO - ✅ Hoàn thành batch 16: 400 records
2025-07-27 12:42:12,722 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:42:12,722 - INFO - 😴 Xử lý batch trong 8.27s, nghỉ 52.72720789909363 giây để tránh rate limit...
2025-07-27 12:43:05,455 - INFO - 🔄 Xử lý batch 17 (offset: 6400)
2025-07-27 12:43:05,511 - INFO - 📊 Lấy được 400 records brand_office (offset: 6400, excluded: 6800)
2025-07-27 12:43:05,512 - INFO - 📊 Batch 17: 400 records
2025-07-27 12:43:05,629 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:43:06,044 - INFO - 🚀 Xử lý 248 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:43:08,365 - INFO - 📊 Progress: 4200 requests | 256.3 RPM | 100.0% success
2025-07-27 12:43:12,351 - INFO - 📊 Batch RPM: 2359.6 RPM (248 records trong 6.31s)
2025-07-27 12:43:12,353 - INFO - 🔄 Đã xử lý 6800 records...
2025-07-27 12:43:12,355 - INFO - ✅ Hoàn thành batch 17: 400 records
2025-07-27 12:43:12,366 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:43:12,367 - INFO - 😴 Xử lý batch trong 6.91s, nghỉ 54.09070110321045 giây để tránh rate limit...
2025-07-27 12:44:06,458 - INFO - 🔄 Xử lý batch 18 (offset: 6800)
2025-07-27 12:44:06,517 - INFO - 📊 Lấy được 400 records brand_office (offset: 6800, excluded: 7200)
2025-07-27 12:44:06,517 - INFO - 📊 Batch 18: 400 records
2025-07-27 12:44:06,601 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:44:07,058 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:44:08,252 - INFO - 📊 Progress: 4400 requests | 253.0 RPM | 100.0% success
2025-07-27 12:44:13,604 - INFO - 📊 Batch RPM: 2447.4 RPM (267 records trong 6.55s)
2025-07-27 12:44:13,605 - INFO - 🔄 Đã xử lý 7200 records...
2025-07-27 12:44:13,609 - INFO - ✅ Hoàn thành batch 18: 400 records
2025-07-27 12:44:13,617 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:44:13,617 - INFO - 😴 Xử lý batch trong 7.16s, nghỉ 53.84280180931091 giây để tránh rate limit...
2025-07-27 12:45:07,465 - INFO - 🔄 Xử lý batch 19 (offset: 7200)
2025-07-27 12:45:07,560 - INFO - 📊 Lấy được 400 records brand_office (offset: 7200, excluded: 7600)
2025-07-27 12:45:07,561 - INFO - 📊 Batch 19: 400 records
2025-07-27 12:45:07,715 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:45:08,185 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:45:08,808 - INFO - 📊 Progress: 4600 requests | 250.0 RPM | 100.0% success
2025-07-27 12:45:16,216 - INFO - 📊 Progress: 4800 requests | 259.2 RPM | 100.0% success
2025-07-27 12:45:16,662 - INFO - 📊 Batch RPM: 1890.0 RPM (267 records trong 8.48s)
2025-07-27 12:45:16,662 - INFO - 🔄 Đã xử lý 7600 records...
2025-07-27 12:45:16,663 - INFO - ✅ Hoàn thành batch 19: 400 records
2025-07-27 12:45:16,673 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:45:16,673 - INFO - 😴 Xử lý batch trong 9.20s, nghỉ 51.795750856399536 giây để tránh rate limit...
2025-07-27 12:46:08,472 - INFO - 🔄 Xử lý batch 20 (offset: 7600)
2025-07-27 12:46:08,526 - INFO - 📊 Lấy được 400 records brand_office (offset: 7600, excluded: 8000)
2025-07-27 12:46:08,527 - INFO - 📊 Batch 20: 400 records
2025-07-27 12:46:08,594 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:46:09,020 - INFO - 🚀 Xử lý 272 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:46:12,061 - INFO - 📊 Progress: 5000 requests | 257.1 RPM | 100.0% success
2025-07-27 12:46:15,808 - INFO - 📊 Batch RPM: 2404.4 RPM (272 records trong 6.79s)
2025-07-27 12:46:15,809 - INFO - 🔄 Đã xử lý 8000 records...
2025-07-27 12:46:15,810 - INFO - ✅ Hoàn thành batch 20: 400 records
2025-07-27 12:46:15,819 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:46:15,819 - INFO - 😴 Xử lý batch trong 7.34s, nghỉ 53.65734815597534 giây để tránh rate limit...
2025-07-27 12:47:09,479 - INFO - 🔄 Xử lý batch 21 (offset: 8000)
2025-07-27 12:47:09,538 - INFO - 📊 Lấy được 400 records brand_office (offset: 8000, excluded: 8400)
2025-07-27 12:47:09,539 - INFO - 📊 Batch 21: 400 records
2025-07-27 12:47:09,704 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:47:10,088 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:47:11,134 - INFO - 📊 Progress: 5200 requests | 254.5 RPM | 100.0% success
2025-07-27 12:47:15,727 - INFO - 📊 Batch RPM: 2532.6 RPM (238 records trong 5.64s)
2025-07-27 12:47:15,728 - INFO - 🔄 Đã xử lý 8400 records...
2025-07-27 12:47:15,734 - INFO - ✅ Hoàn thành batch 21: 400 records
2025-07-27 12:47:15,743 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:47:15,744 - INFO - 😴 Xử lý batch trong 6.26s, nghỉ 54.73626112937927 giây để tránh rate limit...
2025-07-27 12:48:10,481 - INFO - 🔄 Xử lý batch 22 (offset: 8400)
2025-07-27 12:48:10,554 - INFO - 📊 Lấy được 400 records brand_office (offset: 8400, excluded: 8800)
2025-07-27 12:48:10,555 - INFO - 📊 Batch 22: 400 records
2025-07-27 12:48:10,732 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:48:11,167 - INFO - 🚀 Xử lý 230 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:48:11,811 - INFO - 📊 Progress: 5400 requests | 251.8 RPM | 100.0% success
2025-07-27 12:48:15,955 - INFO - 📊 Batch RPM: 2884.2 RPM (230 records trong 4.78s)
2025-07-27 12:48:15,956 - INFO - 🔄 Đã xử lý 8800 records...
2025-07-27 12:48:15,957 - INFO - ✅ Hoàn thành batch 22: 400 records
2025-07-27 12:48:15,964 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:48:15,964 - INFO - 😴 Xử lý batch trong 5.48s, nghỉ 55.51894211769104 giây để tránh rate limit...
2025-07-27 12:49:11,489 - INFO - 🔄 Xử lý batch 23 (offset: 8800)
2025-07-27 12:49:11,582 - INFO - 📊 Lấy được 400 records brand_office (offset: 8800, excluded: 9200)
2025-07-27 12:49:11,583 - INFO - 📊 Batch 23: 400 records
2025-07-27 12:49:11,790 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:49:12,216 - INFO - 🚀 Xử lý 236 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:49:12,846 - INFO - 📊 Progress: 5600 requests | 249.3 RPM | 100.0% success
2025-07-27 12:49:17,802 - INFO - 📊 Batch RPM: 2535.0 RPM (236 records trong 5.59s)
2025-07-27 12:49:17,803 - INFO - 🔄 Đã xử lý 9200 records...
2025-07-27 12:49:17,805 - INFO - ✅ Hoàn thành batch 23: 400 records
2025-07-27 12:49:17,812 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:49:17,813 - INFO - 😴 Xử lý batch trong 6.32s, nghỉ 54.68032622337341 giây để tránh rate limit...
2025-07-27 12:50:12,495 - INFO - 🔄 Xử lý batch 24 (offset: 9200)
2025-07-27 12:50:12,576 - INFO - 📊 Lấy được 400 records brand_office (offset: 9200, excluded: 9600)
2025-07-27 12:50:12,576 - INFO - 📊 Batch 24: 400 records
2025-07-27 12:50:12,675 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:50:13,142 - INFO - 🚀 Xử lý 241 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:50:13,647 - INFO - 📊 Progress: 5800 requests | 247.0 RPM | 100.0% success
2025-07-27 12:50:17,692 - INFO - 📊 Progress: 6000 requests | 254.8 RPM | 100.0% success
2025-07-27 12:50:18,806 - INFO - 📊 Batch RPM: 2553.2 RPM (241 records trong 5.66s)
2025-07-27 12:50:18,807 - INFO - 🔄 Đã xử lý 9600 records...
2025-07-27 12:50:18,809 - INFO - ✅ Hoàn thành batch 24: 400 records
2025-07-27 12:50:18,816 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:50:18,817 - INFO - 😴 Xử lý batch trong 6.32s, nghỉ 54.6809446811676 giây để tránh rate limit...
2025-07-27 12:51:13,502 - INFO - 🔄 Xử lý batch 25 (offset: 9600)
2025-07-27 12:51:13,596 - INFO - 📊 Lấy được 400 records brand_office (offset: 9600, excluded: 10000)
2025-07-27 12:51:13,599 - INFO - 📊 Batch 25: 400 records
2025-07-27 12:51:13,774 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:51:14,225 - INFO - 🚀 Xử lý 274 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:51:16,424 - INFO - 📊 Progress: 6200 requests | 252.8 RPM | 100.0% success
2025-07-27 12:51:22,510 - INFO - 📊 Batch RPM: 1984.4 RPM (274 records trong 8.28s)
2025-07-27 12:51:22,510 - INFO - 🔄 Đã xử lý 10000 records...
2025-07-27 12:51:22,512 - INFO - ✅ Hoàn thành batch 25: 400 records
2025-07-27 12:51:22,523 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:51:22,524 - INFO - 😴 Xử lý batch trong 9.02s, nghỉ 51.98056697845459 giây để tránh rate limit...
2025-07-27 12:52:14,507 - INFO - 🔄 Xử lý batch 26 (offset: 10000)
2025-07-27 12:52:14,584 - INFO - 📊 Lấy được 400 records brand_office (offset: 10000, excluded: 10400)
2025-07-27 12:52:14,585 - INFO - 📊 Batch 26: 400 records
2025-07-27 12:52:14,730 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:52:15,141 - INFO - 🚀 Xử lý 260 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:52:15,866 - INFO - 📊 Progress: 6400 requests | 250.8 RPM | 100.0% success
2025-07-27 12:52:23,753 - INFO - 📊 Batch RPM: 1811.5 RPM (260 records trong 8.61s)
2025-07-27 12:52:23,754 - INFO - 🔄 Đã xử lý 10400 records...
2025-07-27 12:52:23,755 - INFO - ✅ Hoàn thành batch 26: 400 records
2025-07-27 12:52:23,760 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:52:23,760 - INFO - 😴 Xử lý batch trong 9.25s, nghỉ 51.750434160232544 giây để tránh rate limit...
2025-07-27 12:53:15,514 - INFO - 🔄 Xử lý batch 27 (offset: 10400)
2025-07-27 12:53:15,608 - INFO - 📊 Lấy được 400 records brand_office (offset: 10400, excluded: 10800)
2025-07-27 12:53:15,609 - INFO - 📊 Batch 27: 400 records
2025-07-27 12:53:15,779 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:53:16,159 - INFO - 🚀 Xử lý 232 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:53:16,681 - INFO - 📊 Progress: 6600 requests | 248.8 RPM | 100.0% success
2025-07-27 12:53:20,474 - INFO - 📊 Batch RPM: 3226.6 RPM (232 records trong 4.31s)
2025-07-27 12:53:20,474 - INFO - 🔄 Đã xử lý 10800 records...
2025-07-27 12:53:20,475 - INFO - ✅ Hoàn thành batch 27: 400 records
2025-07-27 12:53:20,480 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:53:20,480 - INFO - 😴 Xử lý batch trong 4.96s, nghỉ 56.035341024398804 giây để tránh rate limit...
2025-07-27 12:54:16,518 - INFO - 🔄 Xử lý batch 28 (offset: 10800)
2025-07-27 12:54:16,614 - INFO - 📊 Lấy được 400 records brand_office (offset: 10800, excluded: 11200)
2025-07-27 12:54:16,614 - INFO - 📊 Batch 28: 400 records
2025-07-27 12:54:16,761 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:54:17,182 - INFO - 🚀 Xử lý 281 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:54:17,649 - INFO - 📊 Progress: 6800 requests | 246.9 RPM | 100.0% success
2025-07-27 12:54:20,674 - INFO - 📊 Progress: 7000 requests | 253.7 RPM | 100.0% success
2025-07-27 12:54:25,672 - INFO - 📊 Batch RPM: 1985.8 RPM (281 records trong 8.49s)
2025-07-27 12:54:25,673 - INFO - 🔄 Đã xử lý 11200 records...
2025-07-27 12:54:25,674 - INFO - ✅ Hoàn thành batch 28: 400 records
2025-07-27 12:54:25,679 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:54:25,679 - INFO - 😴 Xử lý batch trong 9.16s, nghỉ 51.84006714820862 giây để tránh rate limit...
2025-07-27 12:55:17,525 - INFO - 🔄 Xử lý batch 29 (offset: 11200)
2025-07-27 12:55:17,636 - INFO - 📊 Lấy được 400 records brand_office (offset: 11200, excluded: 11600)
2025-07-27 12:55:17,637 - INFO - 📊 Batch 29: 400 records
2025-07-27 12:55:17,834 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:55:18,197 - INFO - 🚀 Xử lý 243 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:55:19,187 - INFO - 📊 Progress: 7200 requests | 252.0 RPM | 100.0% success
2025-07-27 12:55:22,832 - INFO - 📊 Batch RPM: 3145.8 RPM (243 records trong 4.63s)
2025-07-27 12:55:22,832 - INFO - 🔄 Đã xử lý 11600 records...
2025-07-27 12:55:22,834 - INFO - ✅ Hoàn thành batch 29: 400 records
2025-07-27 12:55:22,846 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:55:22,846 - INFO - 😴 Xử lý batch trong 5.32s, nghỉ 55.68249702453613 giây để tránh rate limit...
2025-07-27 12:56:18,560 - INFO - 🔄 Xử lý batch 30 (offset: 11600)
2025-07-27 12:56:18,655 - INFO - 📊 Lấy được 400 records brand_office (offset: 11600, excluded: 12000)
2025-07-27 12:56:18,656 - INFO - 📊 Batch 30: 400 records
2025-07-27 12:56:18,835 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:56:19,211 - INFO - 🚀 Xử lý 219 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:56:19,905 - INFO - 📊 Progress: 7400 requests | 250.2 RPM | 100.0% success
2025-07-27 12:56:22,711 - INFO - 📊 Batch RPM: 3754.8 RPM (219 records trong 3.50s)
2025-07-27 12:56:22,711 - INFO - 🔄 Đã xử lý 12000 records...
2025-07-27 12:56:22,712 - INFO - ✅ Hoàn thành batch 30: 400 records
2025-07-27 12:56:22,724 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:56:22,724 - INFO - 😴 Xử lý batch trong 4.16s, nghỉ 56.83909487724304 giây để tránh rate limit...
2025-07-27 12:57:19,660 - INFO - 🔄 Xử lý batch 31 (offset: 12000)
2025-07-27 12:57:19,760 - INFO - 📊 Lấy được 400 records brand_office (offset: 12000, excluded: 12400)
2025-07-27 12:57:19,761 - INFO - 📊 Batch 31: 400 records
2025-07-27 12:57:19,922 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 12:57:20,373 - INFO - 🚀 Xử lý 238 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 12:57:21,028 - INFO - 📊 Progress: 7600 requests | 248.4 RPM | 100.0% success
2025-07-27 12:57:26,336 - INFO - 📊 Batch RPM: 2394.7 RPM (238 records trong 5.96s)
2025-07-27 12:57:26,337 - INFO - 🔄 Đã xử lý 12400 records...
2025-07-27 12:57:26,337 - INFO - ✅ Hoàn thành batch 31: 400 records
2025-07-27 12:57:26,346 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 12:57:26,346 - INFO - 😴 Xử lý batch trong 6.68s, nghỉ 54.316747188568115 giây để tránh rate limit...
2025-07-27 13:14:32,269 - INFO - 🔄 Xử lý batch 32 (offset: 12400)
2025-07-27 13:14:32,365 - INFO - 📊 Lấy được 400 records brand_office (offset: 12400, excluded: 12800)
2025-07-27 13:14:32,366 - INFO - 📊 Batch 32: 400 records
2025-07-27 13:14:32,522 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:14:34,284 - INFO - 🚀 Xử lý 247 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 13:14:35,754 - INFO - 📊 Progress: 7800 requests | 163.0 RPM | 100.0% success
2025-07-27 13:14:38,593 - INFO - 📊 Progress: 8000 requests | 167.0 RPM | 100.0% success
2025-07-27 13:14:39,066 - INFO - 📊 Batch RPM: 3100.2 RPM (247 records trong 4.78s)
2025-07-27 13:14:39,067 - INFO - 🔄 Đã xử lý 12800 records...
2025-07-27 13:14:39,067 - INFO - ✅ Hoàn thành batch 32: 400 records
2025-07-27 13:14:39,080 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 13:14:39,080 - INFO - 😴 Xử lý batch trong 6.81s, nghỉ 54.19095492362976 giây để tránh rate limit...
2025-07-27 13:31:38,091 - INFO - 🔄 Xử lý batch 33 (offset: 12800)
2025-07-27 13:31:38,171 - INFO - 📊 Lấy được 0 records brand_office (offset: 12800, excluded: 13200)
2025-07-27 13:31:38,173 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 13:31:38,173 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 13:31:38,173 - INFO - 📊 Tổng xử lý: 12800
2025-07-27 13:31:38,173 - INFO - 📊 Matched: 8021
2025-07-27 13:31:38,173 - INFO - 📊 Unmatched: 4779
2025-07-27 13:31:38,173 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 13:37:25,024 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:37:25,057 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:37:25,058 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:37:26,163 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:37:30,016 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:37:30,017 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:37:30,085 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:37:30,161 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:37:30,162 - INFO - ============================================================
2025-07-27 13:37:30,162 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:37:30,162 - INFO - ============================================================
2025-07-27 13:37:30,162 - INFO - 📊 Tổng records: 25989
2025-07-27 13:37:30,162 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:37:30,162 - INFO - 📊 Còn lại: 12789
2025-07-27 13:37:30,162 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:37:30,162 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:37:30,178 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:37:30,178 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:37:30,242 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:37:30,243 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 13:40:44,939 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:40:44,975 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:40:44,976 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:40:45,807 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:40:49,710 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:40:49,711 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:40:49,783 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:40:49,847 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:40:49,847 - INFO - ============================================================
2025-07-27 13:40:49,847 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:40:49,847 - INFO - ============================================================
2025-07-27 13:40:49,847 - INFO - 📊 Tổng records: 25989
2025-07-27 13:40:49,847 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:40:49,847 - INFO - 📊 Còn lại: 12789
2025-07-27 13:40:49,847 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:40:49,847 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:40:49,862 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:40:49,862 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:40:49,921 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:40:49,922 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 13:40:58,465 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 13:40:58,481 - INFO - ✅ Kết nối database thành công!
2025-07-27 13:40:58,482 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 13:40:59,201 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 13:41:03,014 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 13:41:03,015 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 13:41:03,095 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 13:41:03,160 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 13:41:03,160 - INFO - ============================================================
2025-07-27 13:41:03,160 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 13:41:03,160 - INFO - ============================================================
2025-07-27 13:41:03,160 - INFO - 📊 Tổng records: 25989
2025-07-27 13:41:03,160 - INFO - 📊 Đã xử lý: 13200
2025-07-27 13:41:03,160 - INFO - 📊 Còn lại: 12789
2025-07-27 13:41:03,160 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 13:41:03,160 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 13:41:03,178 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 13:41:03,178 - INFO - 📊 Batch 1: 400 records
2025-07-27 13:41:03,239 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 13:41:03,239 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 14:08:50,790 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 14:08:50,790 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 14:08:50,823 - INFO - ✅ Kết nối database thành công!
2025-07-27 14:08:50,823 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 14:08:52,027 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 14:08:56,089 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 14:08:56,090 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 14:08:56,156 - INFO - 📊 Đã đọc 13200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 14:08:56,228 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 14:08:56,228 - INFO - ============================================================
2025-07-27 14:08:56,228 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 14:08:56,228 - INFO - ============================================================
2025-07-27 14:08:56,228 - INFO - 📊 Tổng records: 25989
2025-07-27 14:08:56,228 - INFO - 📊 Đã xử lý: 13200
2025-07-27 14:08:56,228 - INFO - 📊 Còn lại: 12789
2025-07-27 14:08:56,228 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 14:08:56,228 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 14:08:56,243 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 13200)
2025-07-27 14:08:56,243 - INFO - 📊 Batch 1: 400 records
2025-07-27 14:08:56,305 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:08:57,345 - INFO - 🚀 Xử lý 271 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:09:00,936 - INFO - 📊 Progress: 200 requests | 1182.8 RPM | 100.0% success
2025-07-27 14:09:04,900 - INFO - 📊 Batch RPM: 2152.4 RPM (271 records trong 7.55s)
2025-07-27 14:09:04,901 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 14:09:04,901 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 14:09:04,905 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:09:04,905 - INFO - 😴 Xử lý batch trong 8.68s, nghỉ 52.32297229766846 giây để tránh rate limit...
2025-07-27 14:09:57,232 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 14:09:57,290 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 13600)
2025-07-27 14:09:57,291 - INFO - 📊 Batch 2: 400 records
2025-07-27 14:09:57,382 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:09:57,821 - INFO - 🚀 Xử lý 255 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:09:58,791 - INFO - 📊 Progress: 400 requests | 352.9 RPM | 100.0% success
2025-07-27 14:10:02,140 - INFO - 📊 Batch RPM: 3543.2 RPM (255 records trong 4.32s)
2025-07-27 14:10:02,140 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 14:10:02,141 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 14:10:02,152 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:10:02,153 - INFO - 😴 Xử lý batch trong 4.92s, nghỉ 56.081576108932495 giây để tránh rate limit...
2025-07-27 14:10:58,236 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 14:10:58,291 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 14000)
2025-07-27 14:10:58,292 - INFO - 📊 Batch 3: 400 records
2025-07-27 14:10:58,404 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:10:58,791 - INFO - 🚀 Xử lý 286 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:10:59,394 - INFO - 📊 Progress: 600 requests | 279.9 RPM | 100.0% success
2025-07-27 14:11:04,081 - INFO - 📊 Progress: 800 requests | 360.1 RPM | 100.0% success
2025-07-27 14:11:04,342 - INFO - 📊 Batch RPM: 3091.8 RPM (286 records trong 5.55s)
2025-07-27 14:11:04,342 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 14:11:04,343 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 14:11:04,347 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:11:04,347 - INFO - 😴 Xử lý batch trong 6.11s, nghỉ 54.8911509513855 giây để tránh rate limit...
2025-07-27 14:11:59,240 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 14:11:59,299 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 14400)
2025-07-27 14:11:59,300 - INFO - 📊 Batch 4: 400 records
2025-07-27 14:11:59,434 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:11:59,846 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:12:01,239 - INFO - 📊 Progress: 1000 requests | 315.0 RPM | 100.0% success
2025-07-27 14:12:04,071 - INFO - 📊 Batch RPM: 3678.9 RPM (259 records trong 4.22s)
2025-07-27 14:12:04,071 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 14:12:04,072 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 14:12:04,082 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:12:04,082 - INFO - 😴 Xử lý batch trong 4.84s, nghỉ 56.16030693054199 giây để tránh rate limit...
2025-07-27 14:13:00,250 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 14:13:00,314 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 14800)
2025-07-27 14:13:00,314 - INFO - 📊 Batch 5: 400 records
2025-07-27 14:13:00,413 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:13:00,971 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:13:02,095 - INFO - 📊 Progress: 1200 requests | 286.5 RPM | 100.0% success
2025-07-27 14:13:06,986 - INFO - 📊 Batch RPM: 2583.8 RPM (259 records trong 6.01s)
2025-07-27 14:13:06,987 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 14:13:06,987 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 14:13:06,996 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:13:06,997 - INFO - 😴 Xử lý batch trong 6.74s, nghỉ 54.25599789619446 giây để tránh rate limit...
2025-07-27 14:14:01,258 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 14:14:01,331 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 15200)
2025-07-27 14:14:01,332 - INFO - 📊 Batch 6: 400 records
2025-07-27 14:14:01,436 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:14:01,830 - INFO - 🚀 Xử lý 253 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:14:02,444 - INFO - 📊 Progress: 1400 requests | 269.5 RPM | 100.0% success
2025-07-27 14:14:07,484 - INFO - 📊 Batch RPM: 2685.2 RPM (253 records trong 5.65s)
2025-07-27 14:14:07,484 - INFO - 🔄 Đã xử lý 2400 records...
2025-07-27 14:14:07,485 - INFO - ✅ Hoàn thành batch 6: 400 records
2025-07-27 14:14:07,493 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:14:07,493 - INFO - 😴 Xử lý batch trong 6.23s, nghỉ 54.76627516746521 giây để tránh rate limit...
2025-07-27 14:15:02,261 - INFO - 🔄 Xử lý batch 7 (offset: 2400)
2025-07-27 14:15:02,324 - INFO - 📊 Lấy được 400 records brand_office (offset: 2400, excluded: 15600)
2025-07-27 14:15:02,324 - INFO - 📊 Batch 7: 400 records
2025-07-27 14:15:02,471 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:15:02,960 - INFO - 🚀 Xử lý 243 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:15:03,482 - INFO - 📊 Progress: 1600 requests | 257.6 RPM | 100.0% success
2025-07-27 14:15:04,742 - INFO - 📊 Progress: 1800 requests | 288.8 RPM | 100.0% success
2025-07-27 14:15:05,111 - INFO - 📊 Batch RPM: 6781.2 RPM (243 records trong 2.15s)
2025-07-27 14:15:05,111 - INFO - 🔄 Đã xử lý 2800 records...
2025-07-27 14:15:05,113 - INFO - ✅ Hoàn thành batch 7: 400 records
2025-07-27 14:15:05,121 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:15:05,122 - INFO - 😴 Xử lý batch trong 2.86s, nghỉ 58.14207315444946 giây để tránh rate limit...
2025-07-27 14:16:03,267 - INFO - 🔄 Xử lý batch 8 (offset: 2800)
2025-07-27 14:16:03,350 - INFO - 📊 Lấy được 400 records brand_office (offset: 2800, excluded: 16000)
2025-07-27 14:16:03,350 - INFO - 📊 Batch 8: 400 records
2025-07-27 14:16:03,460 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:16:03,883 - INFO - 🚀 Xử lý 264 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:16:05,314 - INFO - 📊 Progress: 2000 requests | 276.2 RPM | 100.0% success
2025-07-27 14:16:15,744 - INFO - 📊 Batch RPM: 1335.6 RPM (264 records trong 11.86s)
2025-07-27 14:16:15,746 - INFO - 🔄 Đã xử lý 3200 records...
2025-07-27 14:16:15,747 - INFO - ✅ Hoàn thành batch 8: 400 records
2025-07-27 14:16:15,752 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:16:15,752 - INFO - 😴 Xử lý batch trong 12.48s, nghỉ 48.516895055770874 giây để tránh rate limit...
2025-07-27 14:17:04,272 - INFO - 🔄 Xử lý batch 9 (offset: 3200)
2025-07-27 14:17:04,316 - INFO - 📊 Lấy được 400 records brand_office (offset: 3200, excluded: 16400)
2025-07-27 14:17:04,317 - INFO - 📊 Batch 9: 400 records
2025-07-27 14:17:04,391 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:17:05,093 - INFO - 🚀 Xử lý 269 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:17:06,148 - INFO - 📊 Progress: 2200 requests | 266.5 RPM | 100.0% success
2025-07-27 14:17:10,806 - INFO - 📊 Batch RPM: 2825.4 RPM (269 records trong 5.71s)
2025-07-27 14:17:10,807 - INFO - 🔄 Đã xử lý 3600 records...
2025-07-27 14:17:10,808 - INFO - ✅ Hoàn thành batch 9: 400 records
2025-07-27 14:17:10,812 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:17:10,812 - INFO - 😴 Xử lý batch trong 6.54s, nghỉ 54.459755182266235 giây để tránh rate limit...
2025-07-27 14:18:05,275 - INFO - 🔄 Xử lý batch 10 (offset: 3600)
2025-07-27 14:18:05,359 - INFO - 📊 Lấy được 400 records brand_office (offset: 3600, excluded: 16800)
2025-07-27 14:18:05,360 - INFO - 📊 Batch 10: 400 records
2025-07-27 14:18:05,467 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:18:05,990 - INFO - 🚀 Xử lý 256 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:18:06,513 - INFO - 📊 Progress: 2400 requests | 259.1 RPM | 100.0% success
2025-07-27 14:18:11,892 - INFO - 📊 Progress: 2600 requests | 278.0 RPM | 100.0% success
2025-07-27 14:18:12,483 - INFO - 📊 Batch RPM: 2365.8 RPM (256 records trong 6.49s)
2025-07-27 14:18:12,483 - INFO - 🔄 Đã xử lý 4000 records...
2025-07-27 14:18:12,484 - INFO - ✅ Hoàn thành batch 10: 400 records
2025-07-27 14:18:12,494 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:18:12,495 - INFO - 😴 Xử lý batch trong 7.22s, nghỉ 53.78251385688782 giây để tránh rate limit...
2025-07-27 14:19:06,280 - INFO - 🔄 Xử lý batch 11 (offset: 4000)
2025-07-27 14:19:06,368 - INFO - 📊 Lấy được 400 records brand_office (offset: 4000, excluded: 17200)
2025-07-27 14:19:06,369 - INFO - 📊 Batch 11: 400 records
2025-07-27 14:19:06,525 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:19:07,083 - INFO - 🚀 Xử lý 240 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:19:08,557 - INFO - 📊 Progress: 2800 requests | 271.9 RPM | 100.0% success
2025-07-27 14:19:12,044 - INFO - 📊 Batch RPM: 2903.8 RPM (240 records trong 4.96s)
2025-07-27 14:19:12,045 - INFO - 🔄 Đã xử lý 4400 records...
2025-07-27 14:19:12,046 - INFO - ✅ Hoàn thành batch 11: 400 records
2025-07-27 14:19:12,058 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:19:12,058 - INFO - 😴 Xử lý batch trong 5.78s, nghỉ 55.224836111068726 giây để tránh rate limit...
2025-07-27 14:35:22,969 - INFO - 🔄 Xử lý batch 12 (offset: 4400)
2025-07-27 14:35:23,026 - INFO - 📊 Lấy được 400 records brand_office (offset: 4400, excluded: 17600)
2025-07-27 14:35:23,027 - INFO - 📊 Batch 12: 400 records
2025-07-27 14:35:23,215 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:35:24,523 - INFO - 🚀 Xử lý 243 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:35:26,667 - INFO - 📊 Progress: 3000 requests | 112.8 RPM | 100.0% success
2025-07-27 14:35:30,949 - INFO - 📊 Batch RPM: 2269.0 RPM (243 records trong 6.43s)
2025-07-27 14:35:30,950 - INFO - 🔄 Đã xử lý 4800 records...
2025-07-27 14:35:30,951 - INFO - ✅ Hoàn thành batch 12: 400 records
2025-07-27 14:35:30,963 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:35:30,964 - INFO - 😴 Xử lý batch trong 7.99s, nghỉ 53.006897926330566 giây để tránh rate limit...
2025-07-27 14:53:08,368 - INFO - 🔄 Xử lý batch 13 (offset: 4800)
2025-07-27 14:53:08,439 - INFO - 📊 Lấy được 400 records brand_office (offset: 4800, excluded: 18000)
2025-07-27 14:53:08,440 - INFO - 📊 Batch 13: 400 records
2025-07-27 14:53:08,670 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 14:53:10,620 - INFO - 🚀 Xử lý 227 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 14:53:12,189 - INFO - 📊 Progress: 3200 requests | 72.1 RPM | 100.0% success
2025-07-27 14:53:14,508 - INFO - 📊 Batch RPM: 3503.9 RPM (227 records trong 3.89s)
2025-07-27 14:53:14,508 - INFO - 🔄 Đã xử lý 5200 records...
2025-07-27 14:53:14,508 - INFO - ✅ Hoàn thành batch 13: 400 records
2025-07-27 14:53:14,514 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 14:53:14,514 - INFO - 😴 Xử lý batch trong 6.14s, nghỉ 54.85515117645264 giây để tránh rate limit...
2025-07-27 15:09:45,689 - INFO - 🔄 Xử lý batch 14 (offset: 5200)
2025-07-27 15:09:45,780 - INFO - 📊 Lấy được 400 records brand_office (offset: 5200, excluded: 18400)
2025-07-27 15:09:45,780 - INFO - 📊 Batch 14: 400 records
2025-07-27 15:09:45,923 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:09:46,512 - INFO - 🚀 Xử lý 261 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:09:48,096 - INFO - 📊 Progress: 3400 requests | 55.8 RPM | 100.0% success
2025-07-27 15:09:50,498 - INFO - 📊 Batch RPM: 3929.5 RPM (261 records trong 3.99s)
2025-07-27 15:09:50,498 - INFO - 🔄 Đã xử lý 5600 records...
2025-07-27 15:09:50,500 - INFO - ✅ Hoàn thành batch 14: 400 records
2025-07-27 15:09:50,513 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:09:50,513 - INFO - 😴 Xử lý batch trong 4.82s, nghỉ 56.179129123687744 giây để tránh rate limit...
2025-07-27 15:10:46,695 - INFO - 🔄 Xử lý batch 15 (offset: 5600)
2025-07-27 15:10:46,793 - INFO - 📊 Lấy được 400 records brand_office (offset: 5600, excluded: 18800)
2025-07-27 15:10:46,793 - INFO - 📊 Batch 15: 400 records
2025-07-27 15:10:46,904 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:10:47,406 - INFO - 🚀 Xử lý 281 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:10:47,908 - INFO - 📊 Progress: 3600 requests | 58.1 RPM | 100.0% success
2025-07-27 15:10:49,110 - INFO - 📊 Progress: 3800 requests | 61.3 RPM | 100.0% success
2025-07-27 15:10:53,549 - INFO - 📊 Batch RPM: 2744.9 RPM (281 records trong 6.14s)
2025-07-27 15:10:53,550 - INFO - 🔄 Đã xử lý 6000 records...
2025-07-27 15:10:53,551 - INFO - ✅ Hoàn thành batch 15: 400 records
2025-07-27 15:10:53,561 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:10:53,562 - INFO - 😴 Xử lý batch trong 6.86s, nghỉ 54.135592222213745 giây để tránh rate limit...
2025-07-27 15:11:47,701 - INFO - 🔄 Xử lý batch 16 (offset: 6000)
2025-07-27 15:11:47,797 - INFO - 📊 Lấy được 400 records brand_office (offset: 6000, excluded: 19200)
2025-07-27 15:11:47,798 - INFO - 📊 Batch 16: 400 records
2025-07-27 15:11:47,936 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:11:48,422 - INFO - 🚀 Xử lý 252 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:11:49,767 - INFO - 📊 Progress: 4000 requests | 63.5 RPM | 100.0% success
2025-07-27 15:11:56,621 - INFO - 📊 Batch RPM: 1844.3 RPM (252 records trong 8.20s)
2025-07-27 15:11:56,621 - INFO - 🔄 Đã xử lý 6400 records...
2025-07-27 15:11:56,623 - INFO - ✅ Hoàn thành batch 16: 400 records
2025-07-27 15:11:56,632 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:11:56,633 - INFO - 😴 Xử lý batch trong 8.93s, nghỉ 52.07100486755371 giây để tránh rate limit...
2025-07-27 15:12:48,706 - INFO - 🔄 Xử lý batch 17 (offset: 6400)
2025-07-27 15:12:48,795 - INFO - 📊 Lấy được 0 records brand_office (offset: 6400, excluded: 19600)
2025-07-27 15:12:48,796 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 15:12:48,797 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 15:12:48,797 - INFO - 📊 Tổng xử lý: 6400
2025-07-27 15:12:48,797 - INFO - 📊 Matched: 4119
2025-07-27 15:12:48,797 - INFO - 📊 Unmatched: 2281
2025-07-27 15:12:48,797 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 15:31:41,524 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:31:41,563 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:31:41,563 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:31:42,704 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:31:47,155 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 15:31:47,156 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 15:31:47,234 - INFO - 📊 Đã đọc 19600 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 15:31:47,304 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 15:31:47,304 - INFO - ============================================================
2025-07-27 15:31:47,304 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 15:31:47,304 - INFO - ============================================================
2025-07-27 15:31:47,304 - INFO - 📊 Tổng records: 25989
2025-07-27 15:31:47,304 - INFO - 📊 Đã xử lý: 19600
2025-07-27 15:31:47,304 - INFO - 📊 Còn lại: 6389
2025-07-27 15:31:47,304 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 15:31:47,304 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 15:31:47,325 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 19600)
2025-07-27 15:31:47,326 - INFO - 📊 Batch 1: 400 records
2025-07-27 15:31:47,390 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:31:47,391 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 15:32:15,912 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:32:15,931 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:32:15,931 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:32:16,724 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:32:20,873 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 15:32:20,874 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 15:32:20,943 - INFO - 📊 Đã đọc 19600 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 15:32:21,010 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 15:32:21,010 - INFO - ============================================================
2025-07-27 15:32:21,010 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 15:32:21,010 - INFO - ============================================================
2025-07-27 15:32:21,010 - INFO - 📊 Tổng records: 25989
2025-07-27 15:32:21,010 - INFO - 📊 Đã xử lý: 19600
2025-07-27 15:32:21,010 - INFO - 📊 Còn lại: 6389
2025-07-27 15:32:21,010 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 15:32:21,010 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 15:32:21,029 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 19600)
2025-07-27 15:32:21,029 - INFO - 📊 Batch 1: 400 records
2025-07-27 15:32:21,092 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:32:21,092 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 15:32:28,054 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:32:28,069 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:32:28,069 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:32:28,811 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:32:33,656 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 15:32:33,657 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:32:33,674 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:32:33,674 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:32:33,874 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:32:37,762 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 15:32:37,764 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 15:32:37,842 - INFO - 📊 Đã đọc 19600 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 15:32:37,999 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 15:32:37,999 - INFO - ============================================================
2025-07-27 15:32:37,999 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 15:32:37,999 - INFO - ============================================================
2025-07-27 15:32:37,999 - INFO - 📊 Tổng records: 25989
2025-07-27 15:32:37,999 - INFO - 📊 Đã xử lý: 19600
2025-07-27 15:32:37,999 - INFO - 📊 Còn lại: 6389
2025-07-27 15:32:37,999 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 15:32:37,999 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 15:32:38,018 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 19600)
2025-07-27 15:32:38,018 - INFO - 📊 Batch 1: 400 records
2025-07-27 15:32:38,083 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:32:44,112 - INFO - 🚀 Xử lý 267 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:32:46,778 - INFO - 📊 Progress: 200 requests | 914.5 RPM | 100.0% success
2025-07-27 15:32:50,582 - INFO - 📊 Batch RPM: 2476.4 RPM (267 records trong 6.47s)
2025-07-27 15:32:50,582 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 15:32:50,583 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 15:32:50,594 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:32:50,595 - INFO - 😴 Xử lý batch trong 12.60s, nghỉ 48.403944969177246 giây để tránh rate limit...
2025-07-27 15:33:39,003 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 15:33:39,085 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 20000)
2025-07-27 15:33:39,085 - INFO - 📊 Batch 2: 400 records
2025-07-27 15:33:39,256 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:33:39,832 - INFO - 🚀 Xử lý 229 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:33:41,002 - INFO - 📊 Progress: 400 requests | 356.4 RPM | 100.0% success
2025-07-27 15:33:42,222 - INFO - 📊 Batch RPM: 5749.0 RPM (229 records trong 2.39s)
2025-07-27 15:33:42,223 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 15:33:42,223 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 15:33:42,236 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:33:42,237 - INFO - 😴 Xử lý batch trong 3.23s, nghỉ 57.769004821777344 giây để tránh rate limit...
2025-07-27 15:34:40,009 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 15:34:40,111 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 20400)
2025-07-27 15:34:40,112 - INFO - 📊 Batch 3: 400 records
2025-07-27 15:34:40,266 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:34:40,875 - INFO - 🚀 Xử lý 228 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:34:41,869 - INFO - 📊 Progress: 600 requests | 280.8 RPM | 100.0% success
2025-07-27 15:34:45,465 - INFO - 📊 Batch RPM: 2980.5 RPM (228 records trong 4.59s)
2025-07-27 15:34:45,466 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 15:34:45,467 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 15:34:45,477 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:34:45,478 - INFO - 😴 Xử lý batch trong 5.47s, nghỉ 55.53176498413086 giây để tránh rate limit...
2025-07-27 15:35:41,015 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 15:35:41,072 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 20800)
2025-07-27 15:35:41,073 - INFO - 📊 Batch 4: 400 records
2025-07-27 15:35:41,194 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:35:41,829 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:35:42,495 - INFO - 📊 Progress: 800 requests | 254.2 RPM | 100.0% success
2025-07-27 15:35:48,436 - INFO - 📊 Batch RPM: 2352.2 RPM (259 records trong 6.61s)
2025-07-27 15:35:48,437 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 15:35:48,438 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 15:35:48,448 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:35:48,449 - INFO - 😴 Xử lý batch trong 7.43s, nghỉ 53.56694984436035 giây để tránh rate limit...
2025-07-27 15:36:42,023 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 15:36:42,099 - INFO - 📊 Lấy được 400 records brand_office (offset: 1600, excluded: 21200)
2025-07-27 15:36:42,099 - INFO - 📊 Batch 5: 400 records
2025-07-27 15:36:42,227 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:36:42,701 - INFO - 🚀 Xử lý 261 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:36:43,250 - INFO - 📊 Progress: 1000 requests | 240.4 RPM | 100.0% success
2025-07-27 15:36:44,592 - INFO - 📊 Progress: 1200 requests | 286.9 RPM | 100.0% success
2025-07-27 15:36:46,556 - INFO - 📊 Batch RPM: 4063.6 RPM (261 records trong 3.85s)
2025-07-27 15:36:46,556 - INFO - 🔄 Đã xử lý 2000 records...
2025-07-27 15:36:46,557 - INFO - ✅ Hoàn thành batch 5: 400 records
2025-07-27 15:36:46,571 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:36:46,572 - INFO - 😴 Xử lý batch trong 4.55s, nghỉ 56.453147172927856 giây để tránh rate limit...
2025-07-27 15:37:43,027 - INFO - 🔄 Xử lý batch 6 (offset: 2000)
2025-07-27 15:37:43,132 - INFO - 📊 Lấy được 400 records brand_office (offset: 2000, excluded: 21600)
2025-07-27 15:37:43,133 - INFO - 📊 Batch 6: 400 records
2025-07-27 15:37:43,328 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:37:43,890 - INFO - 🚀 Xử lý 246 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:37:45,214 - INFO - 📊 Progress: 1400 requests | 269.6 RPM | 100.0% success
2025-07-27 15:37:46,382 - INFO - 📊 Batch RPM: 5922.5 RPM (246 records trong 2.49s)
2025-07-27 15:37:46,383 - INFO - 🔄 Đã xử lý 2400 records...
2025-07-27 15:37:46,384 - INFO - ✅ Hoàn thành batch 6: 400 records
2025-07-27 15:37:46,395 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:37:46,395 - INFO - 😴 Xử lý batch trong 3.37s, nghỉ 57.63412523269653 giây để tránh rate limit...
2025-07-27 15:38:44,034 - INFO - 🔄 Xử lý batch 7 (offset: 2400)
2025-07-27 15:38:44,123 - INFO - 📊 Lấy được 400 records brand_office (offset: 2400, excluded: 22000)
2025-07-27 15:38:44,123 - INFO - 📊 Batch 7: 400 records
2025-07-27 15:38:44,260 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:38:44,804 - INFO - 🚀 Xử lý 230 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:38:45,799 - INFO - 📊 Progress: 1600 requests | 258.0 RPM | 100.0% success
2025-07-27 15:38:47,537 - INFO - 📊 Batch RPM: 5050.9 RPM (230 records trong 2.73s)
2025-07-27 15:38:47,539 - INFO - 🔄 Đã xử lý 2800 records...
2025-07-27 15:38:47,539 - INFO - ✅ Hoàn thành batch 7: 400 records
2025-07-27 15:38:47,554 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:38:47,555 - INFO - 😴 Xử lý batch trong 3.52s, nghỉ 57.48263692855835 giây để tránh rate limit...
2025-07-27 15:39:45,042 - INFO - 🔄 Xử lý batch 8 (offset: 2800)
2025-07-27 15:39:45,143 - INFO - 📊 Lấy được 400 records brand_office (offset: 2800, excluded: 22400)
2025-07-27 15:39:45,144 - INFO - 📊 Batch 8: 400 records
2025-07-27 15:39:45,275 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:39:45,710 - INFO - 🚀 Xử lý 269 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:39:46,631 - INFO - 📊 Progress: 1800 requests | 249.4 RPM | 100.0% success
2025-07-27 15:39:52,553 - INFO - 📊 Batch RPM: 2359.0 RPM (269 records trong 6.84s)
2025-07-27 15:39:52,554 - INFO - 🔄 Đã xử lý 3200 records...
2025-07-27 15:39:52,555 - INFO - ✅ Hoàn thành batch 8: 400 records
2025-07-27 15:39:52,568 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:39:52,569 - INFO - 😴 Xử lý batch trong 7.52s, nghỉ 53.47632670402527 giây để tránh rate limit...
2025-07-27 15:40:46,048 - INFO - 🔄 Xử lý batch 9 (offset: 3200)
2025-07-27 15:40:46,113 - INFO - 📊 Lấy được 0 records brand_office (offset: 3200, excluded: 22800)
2025-07-27 15:40:46,114 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 15:40:46,115 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 15:40:46,115 - INFO - 📊 Tổng xử lý: 3200
2025-07-27 15:40:46,115 - INFO - 📊 Matched: 1989
2025-07-27 15:40:46,115 - INFO - 📊 Unmatched: 1211
2025-07-27 15:40:46,115 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 15:56:20,906 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:56:21,025 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:56:21,026 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:56:22,056 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:56:25,939 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 15:56:25,940 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 15:56:26,024 - INFO - 📊 Đã đọc 22800 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 15:56:26,146 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 15:56:26,146 - INFO - ============================================================
2025-07-27 15:56:26,146 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 15:56:26,146 - INFO - ============================================================
2025-07-27 15:56:26,146 - INFO - 📊 Tổng records: 25989
2025-07-27 15:56:26,146 - INFO - 📊 Đã xử lý: 22800
2025-07-27 15:56:26,146 - INFO - 📊 Còn lại: 3189
2025-07-27 15:56:26,146 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 15:56:26,146 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 15:56:26,175 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 22800)
2025-07-27 15:56:26,175 - INFO - 📊 Batch 1: 400 records
2025-07-27 15:56:26,430 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:56:26,431 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 15:56:39,600 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 15:56:39,601 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 15:56:39,621 - INFO - ✅ Kết nối database thành công!
2025-07-27 15:56:39,621 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 15:56:40,293 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 15:56:44,672 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 15:56:44,673 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 15:56:44,751 - INFO - 📊 Đã đọc 22800 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 15:56:44,855 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 15:56:44,855 - INFO - ============================================================
2025-07-27 15:56:44,855 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 15:56:44,855 - INFO - ============================================================
2025-07-27 15:56:44,855 - INFO - 📊 Tổng records: 25989
2025-07-27 15:56:44,855 - INFO - 📊 Đã xử lý: 22800
2025-07-27 15:56:44,855 - INFO - 📊 Còn lại: 3189
2025-07-27 15:56:44,855 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 15:56:44,855 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 15:56:44,879 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 22800)
2025-07-27 15:56:44,879 - INFO - 📊 Batch 1: 400 records
2025-07-27 15:56:45,130 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:56:45,776 - INFO - 🚀 Xử lý 229 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:56:48,455 - INFO - 📊 Progress: 200 requests | 1355.3 RPM | 100.0% success
2025-07-27 15:56:50,220 - INFO - 📊 Batch RPM: 3092.5 RPM (229 records trong 4.44s)
2025-07-27 15:56:50,221 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 15:56:50,223 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 15:56:50,230 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:56:50,230 - INFO - 😴 Xử lý batch trong 5.38s, nghỉ 55.62475371360779 giây để tránh rate limit...
2025-07-27 15:57:45,860 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 15:57:45,928 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 23200)
2025-07-27 15:57:45,928 - INFO - 📊 Batch 2: 400 records
2025-07-27 15:57:46,151 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:57:46,585 - INFO - 🚀 Xử lý 237 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:57:47,961 - INFO - 📊 Progress: 400 requests | 351.1 RPM | 100.0% success
2025-07-27 15:57:50,219 - INFO - 📊 Batch RPM: 3913.5 RPM (237 records trong 3.63s)
2025-07-27 15:57:50,220 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 15:57:50,221 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 15:57:50,232 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:57:50,232 - INFO - 😴 Xử lý batch trong 4.37s, nghỉ 56.629069805145264 giây để tránh rate limit...
2025-07-27 15:58:46,864 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 15:58:46,915 - INFO - 📊 Lấy được 400 records brand_office (offset: 800, excluded: 23600)
2025-07-27 15:58:46,916 - INFO - 📊 Batch 3: 400 records
2025-07-27 15:58:47,019 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:58:47,445 - INFO - 🚀 Xử lý 237 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:58:48,785 - INFO - 📊 Progress: 600 requests | 278.7 RPM | 100.0% success
2025-07-27 15:58:50,288 - INFO - 📊 Batch RPM: 5000.9 RPM (237 records trong 2.84s)
2025-07-27 15:58:50,289 - INFO - 🔄 Đã xử lý 1200 records...
2025-07-27 15:58:50,290 - INFO - ✅ Hoàn thành batch 3: 400 records
2025-07-27 15:58:50,296 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:58:50,297 - INFO - 😴 Xử lý batch trong 3.43s, nghỉ 57.567479848861694 giây để tránh rate limit...
2025-07-27 15:59:47,866 - INFO - 🔄 Xử lý batch 4 (offset: 1200)
2025-07-27 15:59:47,943 - INFO - 📊 Lấy được 400 records brand_office (offset: 1200, excluded: 24000)
2025-07-27 15:59:47,943 - INFO - 📊 Batch 4: 400 records
2025-07-27 15:59:48,079 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 15:59:48,524 - INFO - 🚀 Xử lý 275 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 15:59:49,160 - INFO - 📊 Progress: 800 requests | 253.2 RPM | 100.0% success
2025-07-27 15:59:56,708 - INFO - 📊 Batch RPM: 2016.5 RPM (275 records trong 8.18s)
2025-07-27 15:59:56,708 - INFO - 🔄 Đã xử lý 1600 records...
2025-07-27 15:59:56,709 - INFO - ✅ Hoàn thành batch 4: 400 records
2025-07-27 15:59:56,717 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 15:59:56,718 - INFO - 😴 Xử lý batch trong 8.85s, nghỉ 52.14941596984863 giây để tránh rate limit...
2025-07-27 16:00:48,869 - INFO - 🔄 Xử lý batch 5 (offset: 1600)
2025-07-27 16:00:48,959 - INFO - 📊 Lấy được 0 records brand_office (offset: 1600, excluded: 24400)
2025-07-27 16:00:48,960 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 16:00:48,960 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 16:00:48,960 - INFO - 📊 Tổng xử lý: 1600
2025-07-27 16:00:48,960 - INFO - 📊 Matched: 978
2025-07-27 16:00:48,960 - INFO - 📊 Unmatched: 622
2025-07-27 16:00:48,960 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 16:01:02,613 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:01:02,631 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:01:02,631 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:01:03,425 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:01:07,257 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:01:07,259 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:01:07,344 - INFO - 📊 Đã đọc 24400 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:01:07,459 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:01:07,459 - INFO - ============================================================
2025-07-27 16:01:07,459 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:01:07,459 - INFO - ============================================================
2025-07-27 16:01:07,459 - INFO - 📊 Tổng records: 25989
2025-07-27 16:01:07,459 - INFO - 📊 Đã xử lý: 24400
2025-07-27 16:01:07,459 - INFO - 📊 Còn lại: 1589
2025-07-27 16:01:07,459 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:01:07,459 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:01:07,492 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 24400)
2025-07-27 16:01:07,493 - INFO - 📊 Batch 1: 400 records
2025-07-27 16:01:07,609 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:01:07,609 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 16:01:14,244 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 16:01:14,245 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:01:14,258 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:01:14,258 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:01:14,884 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:01:18,885 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:01:18,886 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:01:18,972 - INFO - 📊 Đã đọc 24400 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:01:19,075 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:01:19,075 - INFO - ============================================================
2025-07-27 16:01:19,075 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:01:19,075 - INFO - ============================================================
2025-07-27 16:01:19,075 - INFO - 📊 Tổng records: 25989
2025-07-27 16:01:19,075 - INFO - 📊 Đã xử lý: 24400
2025-07-27 16:01:19,075 - INFO - 📊 Còn lại: 1589
2025-07-27 16:01:19,075 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:01:19,075 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:01:19,104 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 24400)
2025-07-27 16:01:19,104 - INFO - 📊 Batch 1: 400 records
2025-07-27 16:01:19,210 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:01:20,759 - INFO - 🚀 Xử lý 252 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 16:01:23,263 - INFO - 📊 Progress: 200 requests | 1330.7 RPM | 100.0% success
2025-07-27 16:01:25,466 - INFO - 📊 Batch RPM: 3212.5 RPM (252 records trong 4.71s)
2025-07-27 16:01:25,467 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 16:01:25,467 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 16:01:25,473 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 16:01:25,473 - INFO - 😴 Xử lý batch trong 6.40s, nghỉ 54.602049112319946 giây để tránh rate limit...
2025-07-27 16:02:20,077 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 16:02:20,150 - INFO - 📊 Lấy được 400 records brand_office (offset: 400, excluded: 24800)
2025-07-27 16:02:20,150 - INFO - 📊 Batch 2: 400 records
2025-07-27 16:02:20,286 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:02:20,677 - INFO - 🚀 Xử lý 245 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 16:02:22,006 - INFO - 📊 Progress: 400 requests | 354.2 RPM | 100.0% success
2025-07-27 16:02:25,939 - INFO - 📊 Batch RPM: 2794.0 RPM (245 records trong 5.26s)
2025-07-27 16:02:25,940 - INFO - 🔄 Đã xử lý 800 records...
2025-07-27 16:02:25,941 - INFO - ✅ Hoàn thành batch 2: 400 records
2025-07-27 16:02:25,952 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 16:02:25,952 - INFO - 😴 Xử lý batch trong 5.87s, nghỉ 55.12734007835388 giây để tránh rate limit...
2025-07-27 16:03:21,083 - INFO - 🔄 Xử lý batch 3 (offset: 800)
2025-07-27 16:03:21,192 - INFO - 📊 Lấy được 0 records brand_office (offset: 800, excluded: 25200)
2025-07-27 16:03:21,193 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 16:03:21,193 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 16:03:21,193 - INFO - 📊 Tổng xử lý: 800
2025-07-27 16:03:21,193 - INFO - 📊 Matched: 497
2025-07-27 16:03:21,193 - INFO - 📊 Unmatched: 303
2025-07-27 16:03:21,193 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 16:50:26,761 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 16:50:26,762 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:50:26,785 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:50:26,785 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:50:27,950 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:50:32,025 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:50:32,027 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:50:32,123 - INFO - 📊 Đã đọc 25200 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:50:32,242 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:50:32,242 - INFO - ============================================================
2025-07-27 16:50:32,242 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:50:32,242 - INFO - ============================================================
2025-07-27 16:50:32,242 - INFO - 📊 Tổng records: 25989
2025-07-27 16:50:32,242 - INFO - 📊 Đã xử lý: 25200
2025-07-27 16:50:32,243 - INFO - 📊 Còn lại: 789
2025-07-27 16:50:32,243 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:50:32,243 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:50:32,268 - INFO - 📊 Lấy được 400 records brand_office (offset: 0, excluded: 25200)
2025-07-27 16:50:32,269 - INFO - 📊 Batch 1: 400 records
2025-07-27 16:50:32,366 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:50:38,071 - INFO - 🚀 Xử lý 259 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 16:50:40,770 - INFO - 📊 Progress: 200 requests | 856.6 RPM | 100.0% success
2025-07-27 16:50:42,968 - INFO - 📊 Batch RPM: 3173.9 RPM (259 records trong 4.90s)
2025-07-27 16:50:42,968 - INFO - 🔄 Đã xử lý 400 records...
2025-07-27 16:50:42,968 - INFO - ✅ Hoàn thành batch 1: 400 records
2025-07-27 16:50:42,976 - INFO - ✅ Đã append 400 records vào exports/brand_offices_updated.csv
2025-07-27 16:50:42,977 - INFO - 😴 Xử lý batch trong 10.73s, nghỉ 50.26567769050598 giây để tránh rate limit...
2025-07-27 16:51:33,243 - INFO - 🔄 Xử lý batch 2 (offset: 400)
2025-07-27 16:51:33,331 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25600)
2025-07-27 16:51:33,333 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 16:51:33,334 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 16:51:33,334 - INFO - 📊 Tổng xử lý: 400
2025-07-27 16:51:33,334 - INFO - 📊 Matched: 259
2025-07-27 16:51:33,334 - INFO - 📊 Unmatched: 141
2025-07-27 16:51:33,334 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 16:52:37,599 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:52:37,614 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:52:37,614 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:52:38,524 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:52:42,557 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:52:42,558 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:52:42,651 - INFO - 📊 Đã đọc 25600 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:52:42,756 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:52:42,756 - INFO - ============================================================
2025-07-27 16:52:42,756 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:52:42,756 - INFO - ============================================================
2025-07-27 16:52:42,756 - INFO - 📊 Tổng records: 25989
2025-07-27 16:52:42,757 - INFO - 📊 Đã xử lý: 25600
2025-07-27 16:52:42,757 - INFO - 📊 Còn lại: 389
2025-07-27 16:52:42,757 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:52:42,757 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:52:42,798 - INFO - 📊 Lấy được 389 records brand_office (offset: 0, excluded: 25600)
2025-07-27 16:52:42,798 - INFO - 📊 Batch 1: 389 records
2025-07-27 16:52:42,896 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:52:42,897 - ERROR - ❌ Lỗi trong quá trình xử lý: Event loop is closed
2025-07-27 16:52:47,363 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 16:52:47,364 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:52:47,381 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:52:47,381 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:52:48,153 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:52:52,108 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:52:52,109 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:52:52,201 - INFO - 📊 Đã đọc 25600 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:52:52,472 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:52:52,472 - INFO - ============================================================
2025-07-27 16:52:52,472 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:52:52,472 - INFO - ============================================================
2025-07-27 16:52:52,472 - INFO - 📊 Tổng records: 25989
2025-07-27 16:52:52,472 - INFO - 📊 Đã xử lý: 25600
2025-07-27 16:52:52,472 - INFO - 📊 Còn lại: 389
2025-07-27 16:52:52,472 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:52:52,472 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:52:52,509 - INFO - 📊 Lấy được 389 records brand_office (offset: 0, excluded: 25600)
2025-07-27 16:52:52,510 - INFO - 📊 Batch 1: 389 records
2025-07-27 16:52:52,597 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 16:52:53,224 - INFO - 🚀 Xử lý 264 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 16:52:58,809 - INFO - 📊 Progress: 200 requests | 1048.5 RPM | 100.0% success
2025-07-27 16:53:01,481 - INFO - 📊 Batch RPM: 1918.5 RPM (264 records trong 8.26s)
2025-07-27 16:53:01,485 - INFO - ✅ Hoàn thành batch 1: 389 records
2025-07-27 16:53:01,497 - INFO - ✅ Đã append 389 records vào exports/brand_offices_updated.csv
2025-07-27 16:53:01,498 - INFO - 😴 Xử lý batch trong 9.03s, nghỉ 51.97386407852173 giây để tránh rate limit...
2025-07-27 16:53:53,477 - INFO - 🔄 Xử lý batch 2 (offset: 389)
2025-07-27 16:53:53,549 - INFO - 📊 Lấy được 0 records brand_office (offset: 389, excluded: 25989)
2025-07-27 16:53:53,549 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 16:53:53,549 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 16:53:53,549 - INFO - 📊 Tổng xử lý: 389
2025-07-27 16:53:53,549 - INFO - 📊 Matched: 264
2025-07-27 16:53:53,549 - INFO - 📊 Unmatched: 125
2025-07-27 16:53:53,549 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 16:58:12,366 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 16:58:12,366 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:58:12,399 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:58:12,399 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:58:13,361 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 16:58:17,340 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 16:58:17,342 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 16:58:17,430 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 16:58:17,518 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 16:58:17,518 - INFO - ============================================================
2025-07-27 16:58:17,518 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 16:58:17,518 - INFO - ============================================================
2025-07-27 16:58:17,518 - INFO - 📊 Tổng records: 25989
2025-07-27 16:58:17,518 - INFO - 📊 Đã xử lý: 25988
2025-07-27 16:58:17,518 - INFO - 📊 Còn lại: 1
2025-07-27 16:58:17,518 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 16:58:17,518 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 16:58:17,558 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 16:58:17,558 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 16:58:17,592 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 16:58:17,593 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 16:58:17,593 - INFO - 📊 Batch 1: 1 records
2025-07-27 16:58:17,597 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 16:58:17,599 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 16:58:17,599 - INFO - 😴 Xử lý batch trong 0.08s, nghỉ 60.91936278343201 giây để tránh rate limit...
2025-07-27 16:59:58,028 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 16:59:58,029 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 16:59:58,065 - INFO - ✅ Kết nối database thành công!
2025-07-27 16:59:58,065 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 16:59:58,951 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:00:03,436 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:00:03,438 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:00:03,555 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:00:03,654 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:00:03,654 - INFO - ============================================================
2025-07-27 17:00:03,654 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:00:03,654 - INFO - ============================================================
2025-07-27 17:00:03,654 - INFO - 📊 Tổng records: 25989
2025-07-27 17:00:03,654 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:00:03,654 - INFO - 📊 Còn lại: 1
2025-07-27 17:00:03,654 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:00:03,654 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:00:03,693 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:00:03,693 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:00:03,728 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:00:03,728 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:00:03,728 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:00:03,733 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:00:03,735 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:00:03,735 - INFO - 😴 Xử lý batch trong 0.08s, nghỉ 60.91979432106018 giây để tránh rate limit...
2025-07-27 17:01:04,656 - INFO - 🔄 Xử lý batch 2 (offset: 1)
2025-07-27 17:01:04,722 - INFO - 📊 Lấy được 0 records brand_office (offset: 1, excluded: 25989)
2025-07-27 17:01:04,722 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 17:01:04,722 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 17:01:04,722 - INFO - 📊 Tổng xử lý: 1
2025-07-27 17:01:04,722 - INFO - 📊 Matched: 0
2025-07-27 17:01:04,722 - INFO - 📊 Unmatched: 1
2025-07-27 17:01:04,722 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 17:01:33,488 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:01:33,489 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:01:33,509 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:01:33,509 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:01:34,364 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:01:38,382 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:01:38,383 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:01:38,481 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:01:38,590 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:01:38,590 - INFO - ============================================================
2025-07-27 17:01:38,590 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:01:38,591 - INFO - ============================================================
2025-07-27 17:01:38,591 - INFO - 📊 Tổng records: 25989
2025-07-27 17:01:38,591 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:01:38,591 - INFO - 📊 Còn lại: 1
2025-07-27 17:01:38,591 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:01:38,591 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:01:38,636 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:01:38,636 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:01:38,672 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:01:38,672 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:01:38,672 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:01:38,673 - INFO - Ward info: None, status: no_match
2025-07-27 17:01:38,673 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:01:38,675 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:01:38,675 - INFO - 😴 Xử lý batch trong 0.08s, nghỉ 60.91600489616394 giây để tránh rate limit...
2025-07-27 17:02:35,099 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:02:35,100 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:02:35,116 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:02:35,116 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:02:35,999 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:02:39,894 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:02:39,897 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:02:39,978 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:02:40,097 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:02:40,097 - INFO - ============================================================
2025-07-27 17:02:40,097 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:02:40,097 - INFO - ============================================================
2025-07-27 17:02:40,097 - INFO - 📊 Tổng records: 25989
2025-07-27 17:02:40,097 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:02:40,097 - INFO - 📊 Còn lại: 1
2025-07-27 17:02:40,097 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:02:40,097 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:02:40,141 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:02:40,141 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:02:40,175 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:02:40,175 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:02:40,175 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:02:40,176 - INFO - Ward info: None, status: no_match
2025-07-27 17:02:40,176 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:02:40,178 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:02:40,178 - INFO - 😴 Xử lý batch trong 0.08s, nghỉ 60.919695138931274 giây để tránh rate limit...
2025-07-27 17:03:25,010 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:03:25,010 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:03:25,026 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:03:25,026 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:03:25,831 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:03:29,685 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:03:29,686 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:03:29,783 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:03:29,873 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:03:29,874 - INFO - ============================================================
2025-07-27 17:03:29,874 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:03:29,874 - INFO - ============================================================
2025-07-27 17:03:29,874 - INFO - 📊 Tổng records: 25989
2025-07-27 17:03:29,874 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:03:29,874 - INFO - 📊 Còn lại: 1
2025-07-27 17:03:29,874 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:03:29,874 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:03:29,914 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:03:29,914 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:03:29,999 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:03:29,999 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:03:30,000 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:03:30,003 - INFO - Geometry: MULTIPOLYGON (((105.76481936361337 21.04049627234697, 105.76749556701566 21.039608148748453, 105.7675598818984 21.039715955306754, 105.76767889250387 21.03967719401948, 105.76776124645585 21.0396510082829, 105.76786852775868 21.039616896055186, 105.7681411089552 21.039534542401327, 105.76951625089045 21.03911907010565, 105.76994575519075 21.03898930043518, 105.77089319843942 21.038713158966782, 105.77209819586659 21.03836265379333, 105.77248481729015 21.038253870317877, 105.77253269120659 21.038240399191075, 105.77359764397592 21.037984656321065, 105.77373285569755 21.03795218453134, 105.77458791171084 21.037743574766033, 105.77673475139092 21.03706982597737, 105.7773243471529 21.036901122308382, 105.7774887801724 21.036866562561258, 105.77752246070735 21.036859484431616, 105.7780512629825 21.03676833147323, 105.77883243705524 21.036690785555027, 105.77943947271018 21.036679703850325, 105.77970162446628 21.036692168147937, 105.78029508657727 21.036720380083125, 105.78037593135959 21.036721507789068, 105.7804733338892 21.03672286858216, 105.78057168683638 21.036724241113436, 105.78055399361855 21.036607106380885, 105.78055367561494 21.03660500440247, 105.7805425167425 21.036531140845977, 105.78054035809089 21.03651685962629, 105.78053629411251 21.03648994891821, 105.78052766489589 21.03643282988454, 105.78044597479891 21.0358920974758, 105.7803963743205 21.0355273353729, 105.78035846721215 21.035252626269656, 105.78032837095516 21.035075210700253, 105.78026717322643 21.034714451055468, 105.78007579793496 21.033632928202046, 105.78001574106663 21.03329471886556, 105.78001545540236 21.033293065417798, 105.77999119819474 21.033152113959005, 105.77999065201907 21.033148948760548, 105.7798835908035 21.032526860611537, 105.77986947647376 21.032444299290816, 105.7798561032541 21.03236605854786, 105.77980424800434 21.03206271240817, 105.77972813554696 21.031617446821887, 105.7797249061035 21.031598562759047, 105.7796112773049 21.030925504755714, 105.7794963917632 21.03024282132174, 105.77928373628095 21.028979246607168, 105.77926593796022 21.028879225180074, 105.77923866780314 21.028725980665907, 105.77913693359723 21.028154290026453, 105.77909323505021 21.02789885685667, 105.77895655188814 21.027099890227085, 105.77871896456352 21.025632176652493, 105.77865493085342 21.025151990632672, 105.77862108233352 21.024559151604283, 105.77862139225229 21.02430674936735, 105.77863283768734 21.02401627898577, 105.77867597748222 21.023591863402448, 105.77871823243656 21.02330885612007, 105.77880205423571 21.02292226503513, 105.77890208523587 21.02253864299569, 105.77907440187029 21.022042170390556, 105.77921681808051 21.021713090633767, 105.77950206372591 21.02113399318918, 105.77971875444044 21.02076351893666, 105.78014490982135 21.020189855710477, 105.78041517965265 21.019846503303306, 105.78113291649667 21.018963760832165, 105.78194001774517 21.017952646485305, 105.78213752134351 21.017713334569894, 105.78220019320766 21.017637397139627, 105.7822432719171 21.017585196527843, 105.78225686073239 21.017568734937733, 105.7826591847071 21.0170812392714, 105.78326499955159 21.016332664827214, 105.78385820654101 21.015596040326002, 105.78449641643125 21.01481673336137, 105.78519206999063 21.01395221932756, 105.78610168248778 21.012833590571447, 105.78704899548076 21.01166211509478, 105.78749660173233 21.011110509437792, 105.78796604692677 21.010531988023985, 105.78844301461338 21.00994579291194, 105.78885735445333 21.00943677323256, 105.7895461475789 21.008586202471424, 105.78992937606564 21.008113468744458, 105.79019127898842 21.00778999452286, 105.79020230221526 21.007776380595836, 105.79020725193247 21.00777027051339, 105.79055877078798 21.007336114382838, 105.79069916938207 21.007162706849567, 105.79123222158682 21.006506595090155, 105.79135410230963 21.006356574696177, 105.79143545194513 21.00625644416943, 105.79196873950849 21.005599883118748, 105.79201386727507 21.00554432046633, 105.79202896436178 21.00552573332673, 105.79221160443545 21.005300871859088, 105.7920971626615 21.00520897587546, 105.7920553209322 21.00517537906541, 105.79202375503144 21.005149948735692, 105.79180077700823 21.004970317766585, 105.79115132110896 21.004500248633526, 105.79060990738552 21.004111474491204, 105.78981593142167 21.00355780724162, 105.78920131668069 21.003238641256967, 105.78810809123443 21.002789604640455, 105.78748280540616 21.002594288356676, 105.78739210251193 21.00256595842543, 105.78689766438968 21.002423369416277, 105.78630050481297 21.002305400966584, 105.78580647362753 21.002240072837495, 105.78543609104938 21.002217636780713, 105.78509144250913 21.00219991134579, 105.78490442045353 21.002200763401188, 105.7845812245806 21.002202261207234, 105.78434064137446 21.002203372401517, 105.78367227144689 21.002235424272648, 105.78302477207829 21.002325330325714, 105.78233633468557 21.002454053425254, 105.78228355956095 21.002465161155683, 105.78220190809151 21.00248236995956, 105.78211740626767 21.00250015993585, 105.78158633574645 21.002612030517447, 105.77965783878268 21.00300806106151, 105.77901262832145 21.003140550706775, 105.7756118770487 21.0038514799342, 105.77443031487064 21.00409349014563, 105.77415089210488 21.00415421424414, 105.77407129867576 21.004171513424865, 105.77374805429383 21.004241761764366, 105.77366921005965 21.004258897402913, 105.77364098589175 21.00426503027206, 105.7736312203063 21.00426715283343, 105.77362876521063 21.00426768619931, 105.7735609927123 21.00428241497972, 105.77318204200297 21.004364769658554, 105.7730388334787 21.004395336699513, 105.7729557797391 21.0044130643895, 105.77295135553635 21.00441400868102, 105.7728442889309 21.004436860365423, 105.77250516143641 21.00450924120142, 105.77193373140607 21.004631201434343, 105.77015111725119 21.00500146417088, 105.76856376270314 21.005341851847543, 105.76626436124003 21.005816078707824, 105.76624169764376 21.00582075315825, 105.76621091148064 21.005827310303303, 105.76615757580727 21.005838666013236, 105.7637861303567 21.00634368100773, 105.76331437828861 21.006442727148823, 105.76331361382233 21.006442886485377, 105.76329462253892 21.006363552801815, 105.76289095827777 21.00647618446376, 105.76307649362163 21.00728476386952, 105.76314211106154 21.007570729976383, 105.76320843457728 21.007898546132484, 105.763210821401 21.008367755055346, 105.7630418752455 21.009015990057094, 105.76304129942541 21.009018203119574, 105.76277501811394 21.009524700053053, 105.7627036514562 21.00960544592792, 105.76227858663007 21.010086375527315, 105.76159136555626 21.0107383976777, 105.76151413200137 21.010811675262552, 105.76088299185244 21.011319810573994, 105.7599643396086 21.011937506672275, 105.75926730893188 21.012391631095742, 105.75898815745734 21.01257350052113, 105.75784105106032 21.013589240010212, 105.75684785751254 21.0146584195094, 105.75611390068772 21.015499354106957, 105.755835871209 21.01581790854709, 105.75451778756566 21.017213365903682, 105.75451018781835 21.017222897733184, 105.75398343280372 21.017883472913663, 105.75385100496116 21.018281088860615, 105.75381486124573 21.018732416710208, 105.75387473575601 21.01918331637601, 105.75408843867392 21.01966964127816, 105.75461186453208 21.020641845255298, 105.75525233817686 21.021974467813752, 105.75531285947416 21.022551693764953, 105.75519981727545 21.022985318424794, 105.75505403417957 21.02324396986495, 105.75498788134371 21.023361339824238, 105.7549142886607 21.02349191047271, 105.75441850037058 21.024197933829168, 105.75422935559591 21.024776268905537, 105.75421214387507 21.02517337258932, 105.75430971349134 21.02547973544289, 105.75452297174314 21.025875825178236, 105.75494829797904 21.026433391624046, 105.75573990418718 21.02726001711405, 105.75684025776522 21.028374025726386, 105.75788291973909 21.029470233385368, 105.75815064104994 21.029832438692317, 105.75826685340331 21.029989665691833, 105.75852109728996 21.030333631980433, 105.75883194581931 21.031036067795203, 105.75887568299386 21.032082595867443, 105.75889981623399 21.03305701795309, 105.75892697320333 21.034626965863172, 105.75879792661951 21.035692305307997, 105.75863065312708 21.03679390062275, 105.75852016214374 21.037732820968646, 105.75854729755353 21.03793706501163, 105.75858007258668 21.038183729391214, 105.75858186562402 21.038187426888804, 105.75867502181728 21.03837948283622, 105.75875475987515 21.03854388115545, 105.75914063659535 21.0388850466211, 105.75983807690383 21.039347852207428, 105.760431795933 21.039727477797776, 105.76083725323144 21.040140734957532, 105.7610312498069 21.040518848529626, 105.76132265699687 21.04116722795862, 105.76149589620111 21.041599128326553, 105.76481936361337 21.04049627234697)))
2025-07-27 17:03:30,022 - INFO - Ward info: {'geometry': <MULTIPOLYGON (((105.765 21.04, 105.767 21.04, 105.768 21.04, 105.768 21.04,...>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Từ Liêm', 'code': '00592'}, status: buffer
2025-07-27 17:03:30,023 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 17:03:30,955 - INFO - 🚀 Xử lý 1 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 17:03:31,664 - INFO - 📊 Batch RPM: 84.7 RPM (1 records trong 0.71s)
2025-07-27 17:03:31,665 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:03:31,670 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:03:31,670 - INFO - 😴 Xử lý batch trong 1.80s, nghỉ 59.203564167022705 giây để tránh rate limit...
2025-07-27 17:04:30,880 - INFO - 🔄 Xử lý batch 2 (offset: 1)
2025-07-27 17:04:30,963 - INFO - 📊 Lấy được 0 records brand_office (offset: 1, excluded: 25989)
2025-07-27 17:04:30,964 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 17:04:30,964 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 17:04:30,964 - INFO - 📊 Tổng xử lý: 1
2025-07-27 17:04:30,964 - INFO - 📊 Matched: 1
2025-07-27 17:04:30,964 - INFO - 📊 Unmatched: 0
2025-07-27 17:04:30,964 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 17:05:28,774 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:05:28,774 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:05:28,806 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:05:28,806 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:05:29,682 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:05:33,756 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:05:33,757 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:05:33,861 - INFO - 📊 Đã đọc 25989 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:05:33,953 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:05:33,953 - INFO - ============================================================
2025-07-27 17:05:33,953 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:05:33,953 - INFO - ============================================================
2025-07-27 17:05:33,953 - INFO - 📊 Tổng records: 25989
2025-07-27 17:05:33,953 - INFO - 📊 Đã xử lý: 25989
2025-07-27 17:05:33,953 - INFO - 📊 Còn lại: 0
2025-07-27 17:05:33,953 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:05:33,953 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:05:33,987 - INFO - 📊 Lấy được 0 records brand_office (offset: 0, excluded: 25989)
2025-07-27 17:05:33,987 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 17:05:33,987 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 17:05:33,987 - INFO - 📊 Tổng xử lý: 0
2025-07-27 17:05:33,987 - INFO - 📊 Matched: 0
2025-07-27 17:05:33,987 - INFO - 📊 Unmatched: 0
2025-07-27 17:05:33,988 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv
2025-07-27 17:06:03,165 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:06:03,166 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:06:03,202 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:06:03,203 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:06:04,095 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:06:08,066 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:06:08,068 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:06:08,156 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:06:08,248 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:06:08,248 - INFO - ============================================================
2025-07-27 17:06:08,248 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:06:08,248 - INFO - ============================================================
2025-07-27 17:06:08,248 - INFO - 📊 Tổng records: 25989
2025-07-27 17:06:08,248 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:06:08,248 - INFO - 📊 Còn lại: 1
2025-07-27 17:06:08,248 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:06:08,248 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:06:08,294 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:06:08,295 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:06:08,331 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:06:08,331 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:06:08,331 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:06:08,333 - WARNING - ⚠️ Lỗi xử lý candidate 47: 'MultiPolygon' object has no attribute 'to_dict'
2025-07-27 17:06:08,333 - WARNING - ⚠️ Lỗi xử lý candidate 13: 'MultiPolygon' object has no attribute 'to_dict'
2025-07-27 17:06:08,333 - WARNING - ⚠️ Lỗi xử lý candidate 12: 'MultiPolygon' object has no attribute 'to_dict'
2025-07-27 17:06:08,333 - INFO - Ward info: None, status: error
2025-07-27 17:06:08,333 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:06:08,335 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:06:08,335 - INFO - 😴 Xử lý batch trong 0.09s, nghỉ 60.91368293762207 giây để tránh rate limit...
2025-07-27 17:06:21,207 - INFO - ✅ Gemini instance đã được khởi tạo thành công
2025-07-27 17:06:21,207 - INFO - 🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE (SIMPLE MODE)
2025-07-27 17:06:21,221 - INFO - ✅ Kết nối database thành công!
2025-07-27 17:06:21,221 - INFO - 📊 Lấy dữ liệu geometry...
2025-07-27 17:06:22,532 - INFO - 📊 Lấy được 3321 geo_ward records, đang chuyển đổi thành geometry dataframe
2025-07-27 17:06:26,713 - INFO - 🔍 Tạo spatial index cho GeoDataFrame...
2025-07-27 17:06:26,714 - INFO - ✅ Đã tạo GeoDataFrame với 5270 records và spatial index
2025-07-27 17:06:26,826 - INFO - 📊 Đã đọc 25988 IDs đã xử lý từ exports/brand_offices_updated.csv
2025-07-27 17:06:26,924 - INFO - 📊 Tổng số records brand_office: 25989
2025-07-27 17:06:26,924 - INFO - ============================================================
2025-07-27 17:06:26,924 - INFO - 🔄 XỬ LÝ RECORDS
2025-07-27 17:06:26,924 - INFO - ============================================================
2025-07-27 17:06:26,924 - INFO - 📊 Tổng records: 25989
2025-07-27 17:06:26,924 - INFO - 📊 Đã xử lý: 25988
2025-07-27 17:06:26,924 - INFO - 📊 Còn lại: 1
2025-07-27 17:06:26,924 - INFO - 📊 Dynamic batch size: 300-800 records
2025-07-27 17:06:26,925 - INFO - 🔄 Xử lý batch 1 (offset: 0)
2025-07-27 17:06:26,963 - INFO - 📊 Lấy được 1 records brand_office (offset: 0, excluded: 25988)
2025-07-27 17:06:26,964 - INFO - 📏 Batch hiện tại chỉ có 1 records, lấy thêm...
2025-07-27 17:06:26,998 - INFO - 📊 Lấy được 0 records brand_office (offset: 400, excluded: 25988)
2025-07-27 17:06:26,999 - INFO - ⚠️ Batch cuối chỉ có 1 records (< 300), vẫn xử lý
2025-07-27 17:06:26,999 - INFO - 📊 Batch 1: 1 records
2025-07-27 17:06:27,003 - INFO - Ward info: {'geometry': <MULTIPOLYGON (((105.781 21.037, 105.782 21.037, 105.782 21.037, 105.782 21....>, 'geo_province_code': '01', 'province_title': 'Thành phố Hà Nội', 'ward_title': 'Phường Cầu Giấy', 'code': '00166'}, status: contains
2025-07-27 17:06:27,003 - INFO - Đang gửi hướng dẫn ban đầu cho Gemini...
2025-07-27 17:06:28,620 - INFO - 🚀 Xử lý 1 records với Gemini concurrent (max 100 đồng thời)...
2025-07-27 17:06:29,107 - INFO - 📊 Batch RPM: 123.5 RPM (1 records trong 0.49s)
2025-07-27 17:06:29,108 - INFO - ✅ Hoàn thành batch 1: 1 records
2025-07-27 17:06:29,117 - INFO - ✅ Đã append 1 records vào exports/brand_offices_updated.csv
2025-07-27 17:06:29,117 - INFO - 😴 Xử lý batch trong 2.19s, nghỉ 58.807151794433594 giây để tránh rate limit...
2025-07-27 17:07:27,936 - INFO - 🔄 Xử lý batch 2 (offset: 1)
2025-07-27 17:07:28,026 - INFO - 📊 Lấy được 0 records brand_office (offset: 1, excluded: 25989)
2025-07-27 17:07:28,027 - INFO - ✅ Đã xử lý hết dữ liệu
2025-07-27 17:07:28,027 - INFO - 🎉 HOÀN THÀNH CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE!
2025-07-27 17:07:28,027 - INFO - 📊 Tổng xử lý: 1
2025-07-27 17:07:28,028 - INFO - 📊 Matched: 1
2025-07-27 17:07:28,028 - INFO - 📊 Unmatched: 0
2025-07-27 17:07:28,028 - INFO - 📊 File kết quả: exports/brand_offices_updated.csv

#!/usr/bin/env python3
"""
Script để lọc và loại bỏ các bản ghi unmatched từ file brand_offices_updated.csv
Chỉ giữ lại những records đã match đượ<PERSON> ward thành công
"""

import pandas as pd
import os
from datetime import datetime

def filter_matched_records():
    """
    Lọc các records matched từ file CSV và tạo file mới
    """
    input_file = "exports/brand_offices_updated.csv"
    output_file = "exports/brand_offices_matched_only.csv"
    backup_file = f"exports/brand_offices_updated_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    print("🔍 Đang kiểm tra file brand_offices_updated.csv...")
    
    # Kiểm tra file tồn tại
    if not os.path.exists(input_file):
        print(f"❌ Không tìm thấy file: {input_file}")
        return False
    
    try:
        # Đọc file CSV
        print("📖 Đang đọc file CSV...")
        df = pd.read_csv(input_file)
        
        # Hiển thị thông tin tổng quan
        total_records = len(df)
        print(f"📊 Tổng số records: {total_records:,}")
        
        # Đếm số lượng theo status
        if 'status' in df.columns:
            status_counts = df['status'].value_counts()
            print("\n📈 Phân bố theo status:")
            for status, count in status_counts.items():
                percentage = (count / total_records) * 100
                print(f"   {status}: {count:,} records ({percentage:.1f}%)")
        
        # Lọc chỉ những records matched
        print("\n🔄 Đang lọc records matched...")
        matched_df = df[df['status'] == 'matched'].copy()
        matched_count = len(matched_df)
        
        if matched_count == 0:
            print("⚠️  Không tìm thấy records nào có status 'matched'")
            return False
        
        # Backup file gốc
        print(f"💾 Tạo backup file gốc: {backup_file}")
        df.to_csv(backup_file, index=False)
        
        # Lưu file mới chỉ chứa matched records
        print(f"💾 Lưu file matched: {output_file}")
        matched_df.to_csv(output_file, index=False)
        
        # Báo cáo kết quả
        removed_count = total_records - matched_count
        print(f"\n✅ Hoàn thành!")
        print(f"   📥 Records gốc: {total_records:,}")
        print(f"   ✅ Records matched: {matched_count:,}")
        print(f"   🗑️  Records đã loại bỏ: {removed_count:,}")
        print(f"   📊 Tỷ lệ giữ lại: {(matched_count/total_records)*100:.1f}%")
        
        # Hiển thị thông tin về các cột quan trọng
        print(f"\n📋 Thông tin file mới:")
        print(f"   📁 File: {output_file}")
        print(f"   📏 Kích thước: {os.path.getsize(output_file):,} bytes")
        
        # Kiểm tra các cột quan trọng
        important_cols = ['id', 'latitude', 'longitude', 'geo_province_code', 'ward_code', 'province_title', 'ward_title']
        missing_cols = [col for col in important_cols if col not in matched_df.columns]
        if missing_cols:
            print(f"   ⚠️  Thiếu cột: {missing_cols}")
        else:
            print(f"   ✅ Đầy đủ các cột quan trọng")
        
        # Hiển thị sample data
        print(f"\n📄 Sample 3 records đầu tiên:")
        print(matched_df[['id', 'province_title', 'ward_title', 'status']].head(3).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi xử lý file: {str(e)}")
        return False

def verify_filtered_file():
    """
    Kiểm tra file đã lọc để đảm bảo chất lượng
    """
    output_file = "exports/brand_offices_matched_only.csv"
    
    if not os.path.exists(output_file):
        print(f"❌ File {output_file} không tồn tại")
        return False
    
    try:
        df = pd.read_csv(output_file)
        
        print(f"\n🔍 Kiểm tra chất lượng file {output_file}:")
        
        # Kiểm tra tất cả records đều có status matched
        non_matched = df[df['status'] != 'matched']
        if len(non_matched) > 0:
            print(f"   ⚠️  Có {len(non_matched)} records không phải 'matched'")
        else:
            print(f"   ✅ Tất cả {len(df)} records đều có status 'matched'")
        
        # Kiểm tra dữ liệu ward
        null_ward_code = df['ward_code'].isnull().sum()
        null_ward_title = df['ward_title'].isnull().sum()
        
        print(f"   📊 Ward code null: {null_ward_code}")
        print(f"   📊 Ward title null: {null_ward_title}")
        
        # Kiểm tra tỉnh thành
        provinces = df['province_title'].value_counts()
        print(f"   🏙️  Số tỉnh thành: {len(provinces)}")
        print(f"   🏙️  Top 5 tỉnh thành:")
        for province, count in provinces.head(5).items():
            print(f"      {province}: {count} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra file: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Bắt đầu lọc records matched từ brand_offices_updated.csv")
    print("=" * 60)
    
    # Lọc records
    if filter_matched_records():
        # Kiểm tra chất lượng
        verify_filtered_file()
        print("\n🎉 Hoàn thành! File mới đã được tạo với chỉ các records matched.")
    else:
        print("\n❌ Quá trình lọc thất bại!")
